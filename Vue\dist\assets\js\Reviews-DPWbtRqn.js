import{_ as me}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                        *//* empty css                             *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{r as x,a as L,y as pe,o as _e,g as _,c as z,b as n,G as N,d as e,w as a,aC as ve,l as fe,t as i,s as ge,aD as ye,aJ as we,a1 as be,f as R,ar as T,D as h,i as C,aE as he,k as Ce,e as ke,aK as Ve,aL as xe,q as u,aH as ze,bi as Re,P as Se,m as Z,B as F,aM as Ee,aN as $e,Z as Be,_ as Ie,aZ as De}from"./index-DylWFde9.js";import{b as j}from"./index-Bpa9ymHh.js";const Te={class:"page-container"},Ue={class:"search-bar mb-16"},Ae={key:0,class:"batch-actions"},Ne={class:"batch-info"},Fe={class:"batch-summary"},Pe={class:"summary-item"},Me={class:"summary-value"},Ge={class:"summary-item"},He={class:"summary-value"},Ke={class:"summary-item"},Le={class:"summary-value"},Ze={class:"summary-item"},je={class:"summary-value"},qe={class:"summary-item"},Je={class:"summary-value"},Oe={class:"summary-item"},Qe={class:"summary-value"},We={class:"batch-buttons"},Xe={class:"row-number"},Ye={class:"review-content"},et={class:"action-buttons"},tt={class:"pagination-container"},at={key:0,class:"review-detail"},lt={class:"review-content-detail"},st={key:0,class:"review-images"},nt={__name:"Reviews",setup(ot){const S=x(!1),P=x([]),c=x([]),d=x(null),E=x(!1),v=L({keyword:"",status:null,rating:null}),m=L({current:1,size:10,total:0}),y=pe(()=>{if(c.value.length===0)return{averageRating:"0.0",userCount:0,flowerCount:0,visibleCount:0,hiddenCount:0};const t=(c.value.reduce((r,U)=>r+(U.rating||0),0)/c.value.length).toFixed(1),o=new Set(c.value.map(r=>r.userId)).size,k=new Set(c.value.map(r=>r.flowerId)).size,w=c.value.filter(r=>r.status===1).length,B=c.value.filter(r=>r.status===0).length;return{averageRating:t,userCount:o,flowerCount:k,visibleCount:w,hiddenCount:B}}),f=async()=>{S.value=!0;try{const s={current:m.current,size:m.size,keyword:v.keyword,status:v.status,rating:v.rating},t=await R.getReviews(s);P.value=t.data.records,m.total=t.data.total}catch(s){console.error("加载评价列表失败:",s),_.error("加载评价列表失败")}finally{S.value=!1}},M=()=>{m.current=1,f()},q=()=>{v.keyword="",v.status=null,v.rating=null,m.current=1,f(),_.success("搜索条件已重置")},J=()=>{f(),_({message:"评价数据已刷新",type:"success",duration:1500,showClose:!1})},O=s=>{m.size=s,m.current=1,f()},Q=s=>{m.current=s,f()},W=s=>{d.value=s,E.value=!0},X=async s=>{const t=s.status===1?"隐藏":"显示",p=s.status===1?0:1;try{await T.confirm(`确定要${t}这条评价吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await R.updateReviewStatus(s.id,p),_.success(`${t}成功`),f()}catch(o){o!=="cancel"&&(console.error(`${t}评价失败:`,o),_.error(`${t}评价失败`))}},Y=async s=>{try{await T.confirm("确定要删除这条评价吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await R.deleteReview(s.id),_.success("删除成功"),f()}catch(t){t!=="cancel"&&(console.error("删除评价失败:",t),_.error("删除评价失败"))}},ee=s=>{c.value=s},G=async s=>{if(c.value.length===0){_.warning("请先选择要操作的评价");return}const t=c.value.filter(p=>p.status!==s);if(t.length===0){const p=s===1?"显示":"隐藏";_.warning(`选中的评价都已经是${p}状态`);return}try{const p=s===1?"显示":"隐藏";await T.confirm(`确定要${p}选中的 ${t.length} 条评价吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=t.map($=>R.updateReviewStatus($.id,s));await Promise.all(o),_.success(`批量${p}成功，共操作 ${t.length} 条评价`),c.value=[],f()}catch(p){p!=="cancel"&&(console.error("批量操作失败:",p),_.error("批量操作失败"))}},te=async()=>{if(c.value.length===0){_.warning("请先选择要删除的评价");return}try{await T.confirm(`确定要删除选中的 ${c.value.length} 条评价吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=c.value.map(t=>R.deleteReview(t.id));await Promise.all(s),_.success("批量删除成功"),c.value=[],f()}catch(s){s!=="cancel"&&(console.error("批量删除失败:",s),_.error("批量删除失败"))}};return _e(async()=>{console.log("评价管理页面已挂载，开始加载数据...");try{await f(),console.log("评价数据加载完成")}catch(s){console.error("评价数据加载失败:",s),_.error("评价数据加载失败: "+s.message)}}),(s,t)=>{const p=h("Search"),o=fe,$=Ce,k=he,w=xe,B=Ve,r=ge,U=h("Refresh"),ae=ve,le=h("InfoFilled"),se=h("Star"),ne=h("User"),oe=h("Goods"),I=h("View"),A=h("Hide"),H=h("Delete"),g=ze,K=Re,D=Se,ie=ye,re=we,b=$e,ue=Ee,de=De,ce=be;return C(),z("div",Te,[t[24]||(t[24]=n("div",{class:"page-header"},[n("h2",{class:"page-title"},"评价管理")],-1)),n("div",Ue,[e(ae,{gutter:16},{default:a(()=>[e(k,{span:6},{default:a(()=>[e($,{modelValue:v.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>v.keyword=l),placeholder:"搜索商品名称或用户",clearable:"",onKeyup:ke(M,["enter"])},{prefix:a(()=>[e(o,null,{default:a(()=>[e(p)]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(k,{span:4},{default:a(()=>[e(B,{modelValue:v.status,"onUpdate:modelValue":t[1]||(t[1]=l=>v.status=l),placeholder:"选择状态",clearable:""},{default:a(()=>[e(w,{label:"正常",value:1}),e(w,{label:"隐藏",value:0})]),_:1},8,["modelValue"])]),_:1}),e(k,{span:4},{default:a(()=>[e(B,{modelValue:v.rating,"onUpdate:modelValue":t[2]||(t[2]=l=>v.rating=l),placeholder:"选择评分",clearable:""},{default:a(()=>[e(w,{label:"5星",value:5}),e(w,{label:"4星",value:4}),e(w,{label:"3星",value:3}),e(w,{label:"2星",value:2}),e(w,{label:"1星",value:1})]),_:1},8,["modelValue"])]),_:1}),e(k,{span:6},{default:a(()=>[e(r,{type:"primary",onClick:M},{default:a(()=>[e(o,null,{default:a(()=>[e(p)]),_:1}),t[10]||(t[10]=u(" 搜索 ",-1))]),_:1,__:[10]}),e(r,{onClick:q},{default:a(()=>t[11]||(t[11]=[u(" 重置 ",-1)])),_:1,__:[11]}),e(r,{onClick:J,loading:S.value},{default:a(()=>[e(o,null,{default:a(()=>[e(U)]),_:1}),t[12]||(t[12]=u(" 刷新 ",-1))]),_:1,__:[12]},8,["loading"])]),_:1})]),_:1})]),c.value.length>0?(C(),z("div",Ae,[n("div",Ne,[n("div",Fe,[n("div",Pe,[e(o,null,{default:a(()=>[e(le)]),_:1}),t[13]||(t[13]=n("span",{class:"summary-label"},"已选择:",-1)),n("span",Me,i(c.value.length)+" 条评价",1)]),n("div",Ge,[e(o,null,{default:a(()=>[e(se)]),_:1}),t[14]||(t[14]=n("span",{class:"summary-label"},"平均评分:",-1)),n("span",He,i(y.value.averageRating),1)]),n("div",Ke,[e(o,null,{default:a(()=>[e(ne)]),_:1}),t[15]||(t[15]=n("span",{class:"summary-label"},"用户数:",-1)),n("span",Le,i(y.value.userCount)+" 个",1)]),n("div",Ze,[e(o,null,{default:a(()=>[e(oe)]),_:1}),t[16]||(t[16]=n("span",{class:"summary-label"},"商品数:",-1)),n("span",je,i(y.value.flowerCount)+" 个",1)]),n("div",qe,[e(o,null,{default:a(()=>[e(I)]),_:1}),t[17]||(t[17]=n("span",{class:"summary-label"},"显示:",-1)),n("span",Je,i(y.value.visibleCount),1)]),n("div",Oe,[e(o,null,{default:a(()=>[e(A)]),_:1}),t[18]||(t[18]=n("span",{class:"summary-label"},"隐藏:",-1)),n("span",Qe,i(y.value.hiddenCount),1)])])]),n("div",We,[e(r,{type:"success",size:"small",onClick:t[3]||(t[3]=l=>G(1)),disabled:y.value.hiddenCount===0},{default:a(()=>[e(o,null,{default:a(()=>[e(I)]),_:1}),u(" 批量显示 ("+i(y.value.hiddenCount)+") ",1)]),_:1},8,["disabled"]),e(r,{type:"warning",size:"small",onClick:t[4]||(t[4]=l=>G(0)),disabled:y.value.visibleCount===0},{default:a(()=>[e(o,null,{default:a(()=>[e(A)]),_:1}),u(" 批量隐藏 ("+i(y.value.visibleCount)+") ",1)]),_:1},8,["disabled"]),e(r,{type:"danger",size:"small",onClick:te},{default:a(()=>[e(o,null,{default:a(()=>[e(H)]),_:1}),t[19]||(t[19]=u(" 批量删除 ",-1))]),_:1,__:[19]})])])):N("",!0),e(ie,{data:P.value,loading:S.value,stripe:"",style:{width:"100%"},onSelectionChange:ee},{default:a(()=>[e(g,{type:"selection",width:"55",align:"center"}),e(g,{label:"序号",width:"80",align:"center"},{default:a(({$index:l})=>[n("span",Xe,i((m.current-1)*m.size+l+1),1)]),_:1}),e(g,{prop:"userName",label:"用户",width:"120"}),e(g,{prop:"flowerName",label:"商品",width:"150"}),e(g,{prop:"rating",label:"评分",width:"100"},{default:a(({row:l})=>[e(K,{modelValue:l.rating,"onUpdate:modelValue":V=>l.rating=V,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(g,{prop:"content",label:"评价内容","min-width":"200"},{default:a(({row:l})=>[n("div",Ye,i(l.content||"用户未填写评价内容"),1)]),_:1}),e(g,{prop:"isAnonymous",label:"匿名",width:"80"},{default:a(({row:l})=>[e(D,{type:l.isAnonymous?"info":"success",size:"small"},{default:a(()=>[u(i(l.isAnonymous?"匿名":"实名"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[e(D,{type:l.status===1?"success":"danger"},{default:a(()=>[u(i(l.status===1?"正常":"隐藏"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"createdAt",label:"评价时间",width:"180"},{default:a(({row:l})=>[u(i(Z(j)(l.createdAt)),1)]),_:1}),e(g,{label:"操作",width:"240",fixed:"right",align:"center"},{default:a(({row:l})=>[n("div",et,[e(r,{type:"primary",size:"small",onClick:V=>W(l)},{default:a(()=>[e(o,null,{default:a(()=>[e(I)]),_:1}),t[20]||(t[20]=u(" 详情 ",-1))]),_:2,__:[20]},1032,["onClick"]),e(r,{type:l.status===1?"warning":"success",size:"small",onClick:V=>X(l)},{default:a(()=>[l.status===1?(C(),F(o,{key:0},{default:a(()=>[e(A)]),_:1})):(C(),F(o,{key:1},{default:a(()=>[e(I)]),_:1})),u(" "+i(l.status===1?"隐藏":"显示"),1)]),_:2},1032,["type","onClick"]),e(r,{type:"danger",size:"small",onClick:V=>Y(l)},{default:a(()=>[e(o,null,{default:a(()=>[e(H)]),_:1}),t[21]||(t[21]=u(" 删除 ",-1))]),_:2,__:[21]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),n("div",tt,[e(re,{"current-page":m.current,"onUpdate:currentPage":t[5]||(t[5]=l=>m.current=l),"page-size":m.size,"onUpdate:pageSize":t[6]||(t[6]=l=>m.size=l),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:Q},null,8,["current-page","page-size","total"])]),e(ce,{modelValue:E.value,"onUpdate:modelValue":t[9]||(t[9]=l=>E.value=l),title:"评价详情",width:"600px"},{footer:a(()=>[e(r,{onClick:t[8]||(t[8]=l=>E.value=!1)},{default:a(()=>t[23]||(t[23]=[u("关闭",-1)])),_:1,__:[23]})]),default:a(()=>[d.value?(C(),z("div",at,[e(ue,{column:2,border:""},{default:a(()=>[e(b,{label:"评价ID"},{default:a(()=>[u(i(d.value.id),1)]),_:1}),e(b,{label:"用户"},{default:a(()=>[u(i(d.value.userName),1)]),_:1}),e(b,{label:"商品"},{default:a(()=>[u(i(d.value.flowerName),1)]),_:1}),e(b,{label:"订单号"},{default:a(()=>[u(i(d.value.orderId),1)]),_:1}),e(b,{label:"评分"},{default:a(()=>[e(K,{modelValue:d.value.rating,"onUpdate:modelValue":t[7]||(t[7]=l=>d.value.rating=l),disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value} 分"},null,8,["modelValue"])]),_:1}),e(b,{label:"匿名"},{default:a(()=>[e(D,{type:d.value.isAnonymous?"info":"success",size:"small"},{default:a(()=>[u(i(d.value.isAnonymous?"匿名评价":"实名评价"),1)]),_:1},8,["type"])]),_:1}),e(b,{label:"状态"},{default:a(()=>[e(D,{type:d.value.status===1?"success":"danger"},{default:a(()=>[u(i(d.value.status===1?"正常显示":"已隐藏"),1)]),_:1},8,["type"])]),_:1}),e(b,{label:"评价时间"},{default:a(()=>[u(i(Z(j)(d.value.createdAt)),1)]),_:1}),e(b,{label:"评价内容",span:2},{default:a(()=>[n("div",lt,i(d.value.content||"用户未填写评价内容"),1)]),_:1})]),_:1}),d.value.images&&d.value.images.length>0?(C(),z("div",st,[t[22]||(t[22]=n("h4",null,"评价图片",-1)),(C(!0),z(Be,null,Ie(d.value.images,(l,V)=>(C(),F(de,{key:V,src:l,"preview-src-list":d.value.images,style:{width:"100px",height:"100px","margin-right":"8px"},fit:"cover"},null,8,["src","preview-src-list"]))),128))])):N("",!0)])):N("",!0)]),_:1},8,["modelValue"])])}}},ht=me(nt,[["__scopeId","data-v-dc166447"]]);export{ht as default};
