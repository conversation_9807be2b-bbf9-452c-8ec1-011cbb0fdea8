import{_ as wl}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                *//* empty css                   *//* empty css                     *//* empty css                *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css               */import{r as i,y as F,o as Vl,g as d,B as S,w as a,f as _,C as xl,D as zl,i as f,d as e,F as kl,b as n,q as o,l as Pl,m as r,ao as O,aq as Dl,aC as El,aE as Sl,t as c,bj as oe,bk as he,aI as Ul,bl as $l,bm as Bl,k as Fl,N as we,aD as Tl,aH as Al,P as Rl,s as Ll,aY as Se,aP as Ve,aR as G,aJ as ql,aK as Il,c as U,Z as ae,_ as te,aL as jl,aO as xe,G as se,a1 as Nl,E as Kl,j as Ml,ar as J}from"./index-BSEuU4Y2.js";import{b as ze}from"./index-B9v4HaLW.js";const Hl={class:"header-content"},Ol={class:"title-section"},Gl={class:"page-title"},Jl={class:"stats-section"},Yl={class:"stats-card primary"},Zl={class:"stats-content"},Ql={class:"stats-info"},Wl={class:"stats-value"},Xl={class:"stats-card success"},ea={class:"stats-content"},la={class:"stats-info"},aa={class:"stats-value"},ta={class:"stats-card warning"},sa={class:"stats-content"},na={class:"stats-info"},oa={class:"stats-value"},ia={class:"card-header"},ra={class:"header-actions"},ua={class:"tab-label"},da={class:"search-header"},ca={class:"table-container"},va={class:"row-number"},ma={class:"name-cell"},pa={class:"name-text"},fa={class:"time-text"},_a={class:"table-actions-inline"},ga={class:"pagination-container"},ya={class:"tab-label"},ba={class:"city-header"},Ca={class:"city-filters"},ha={class:"option-item"},wa={class:"filter-tags"},Va={key:1,class:"hint-text"},xa={class:"city-actions"},za={class:"search-header"},ka={class:"table-container"},Pa={class:"row-number"},Da={class:"name-cell"},Ea={class:"name-text"},Sa={class:"time-text"},Ua={class:"table-actions-inline"},$a={key:0,class:"pagination-container"},Ba={class:"tab-label"},Fa={class:"district-header"},Ta={class:"district-filters"},Aa={class:"option-item"},Ra={class:"option-item"},La={class:"filter-tags"},qa={key:2,class:"hint-text"},Ia={class:"district-actions"},ja={class:"search-header"},Na={class:"table-container"},Ka={class:"name-cell"},Ma={class:"name-text"},Ha={class:"time-text"},Oa={class:"table-actions-inline"},Ga={key:0,class:"pagination-container"},Ja={__name:"Addresses",setup(Ya){const ne=i("provinces"),ie=i(!1),re=i(!1),ue=i(!1),de=i(!1),P=i([]),z=i([]),Y=i([]),m=i(""),g=i(""),ce=i({provinceCount:0,cityCount:0,districtCount:0}),V=i({current:1,size:10}),y=i({current:1,size:10}),C=i({current:1,size:10}),T=i([]),A=i([]),R=i([]),Z=i(""),I=i(""),j=i(""),N=i(!1),K=i(!1),M=i(!1),H=i(!1),L=i(!1),q=i(!1),D=i(!1),ve=i(),me=i(),pe=i(),k=i({id:null,code:"",name:""}),h=i({id:null,code:"",name:"",provinceCode:""}),w=i({id:null,code:"",name:"",cityCode:""}),Ue={code:[{required:!0,message:"请输入省份代码",trigger:"blur"},{pattern:/^\d{6}$/,message:"省份代码必须是6位数字",trigger:"blur"}],name:[{required:!0,message:"请输入省份名称",trigger:"blur"},{min:2,max:20,message:"省份名称长度在2到20个字符",trigger:"blur"}]},$e={code:[{required:!0,message:"请输入城市代码",trigger:"blur"},{pattern:/^\d{6}$/,message:"城市代码必须是6位数字",trigger:"blur"}],name:[{required:!0,message:"请输入城市名称",trigger:"blur"},{min:2,max:20,message:"城市名称长度在2到20个字符",trigger:"blur"}],provinceCode:[{required:!0,message:"请选择所属省份",trigger:"change"}]},Be={code:[{required:!0,message:"请输入区县代码",trigger:"blur"},{pattern:/^\d{6}$/,message:"区县代码必须是6位数字",trigger:"blur"}],name:[{required:!0,message:"请输入区县名称",trigger:"blur"},{min:2,max:20,message:"区县名称长度在2到20个字符",trigger:"blur"}],cityCode:[{required:!0,message:"请选择所属城市",trigger:"change"}]},Fe=F(()=>ce.value.cityCount),Te=F(()=>ce.value.districtCount),ke=F(()=>Z.value?P.value.filter(s=>s.name.toLowerCase().includes(Z.value.toLowerCase())||s.code.includes(Z.value)):P.value),Pe=F(()=>I.value?z.value.filter(s=>s.name.toLowerCase().includes(I.value.toLowerCase())||s.code.includes(I.value)):z.value),De=F(()=>j.value?Y.value.filter(s=>s.name.toLowerCase().includes(j.value.toLowerCase())||s.code.includes(j.value)):Y.value),Ae=F(()=>{const s=(V.value.current-1)*V.value.size,l=s+V.value.size;return ke.value.slice(s,l)}),Re=F(()=>{const s=(y.value.current-1)*y.value.size,l=s+y.value.size;return Pe.value.slice(s,l)}),Le=F(()=>{const s=(C.value.current-1)*C.value.size,l=s+C.value.size;return De.value.slice(s,l)}),x=async()=>{try{const s=await _.getAddressStats();ce.value=s.data}catch(s){console.error("加载地址统计失败:",s),d.error("加载地址统计失败")}},Q=async()=>{re.value=!0;try{const s=await _.getProvinces();P.value=s.data}catch(s){console.error("加载省份列表失败:",s),d.error("加载省份列表失败")}finally{re.value=!1}},W=async(s=m.value)=>{if(!s){console.log("没有选择省份，无法加载城市");return}console.log("开始加载城市，省份代码:",s),ue.value=!0;try{const l=await _.getCities(s);console.log("城市数据响应:",l),z.value=l.data||[],console.log("设置城市数据:",z.value),y.value.current=1}catch(l){console.error("加载城市列表失败:",l),d.error("加载城市列表失败: "+(l.message||"未知错误")),z.value=[]}finally{ue.value=!1}},X=async(s=g.value)=>{if(s){de.value=!0;try{const l=await _.getDistricts(s);Y.value=l.data}catch(l){console.error("加载区县列表失败:",l),d.error("加载区县列表失败")}finally{de.value=!1}}},qe=s=>{m.value=s.code,ne.value="cities",W(s.code)},Ie=s=>{g.value=s.code,ne.value="districts",X(s.code)},Ee=s=>{console.log("省份改变:",s),g.value="",Y.value=[],I.value="",j.value="",y.value.current=1,C.value.current=1,s?(console.log("开始加载省份对应的城市:",s),W(s)):(console.log("清空城市列表"),z.value=[])},je=s=>{V.value.size=s,V.value.current=1},Ne=s=>{V.value.current=s},Ke=s=>{y.value.size=s,y.value.current=1},Me=s=>{y.value.current=s},He=s=>{C.value.size=s,C.value.current=1},Oe=s=>{C.value.current=s},Ge=s=>{T.value=s},Je=s=>{A.value=s},Ye=s=>{R.value=s},Ze=()=>{V.value.current=1},Qe=()=>{y.value.current=1},We=()=>{C.value.current=1},Xe=()=>{H.value=!1,k.value={id:null,code:"",name:""},N.value=!0},el=()=>{L.value=!1,h.value={id:null,code:"",name:"",provinceCode:m.value},K.value=!0},ll=()=>{q.value=!1,w.value={id:null,code:"",name:"",cityCode:g.value},M.value=!0},al=s=>{H.value=!0,k.value={id:s.id,code:s.code,name:s.name},N.value=!0},tl=s=>{L.value=!0,h.value={id:s.id,code:s.code,name:s.name,provinceCode:s.provinceCode},K.value=!0},sl=s=>{q.value=!0,w.value={id:s.id,code:s.code,name:s.name,cityCode:s.cityCode},M.value=!0},nl=async s=>{try{await J.confirm(`确定要删除省份"${s.name}"吗？删除后不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await _.deleteProvince(s.id),d.success("省份删除成功"),await Q(),await x()}catch(l){l!=="cancel"&&(console.error("删除省份失败:",l),d.error(l.message||"删除失败"))}},ol=async s=>{try{await J.confirm(`确定要删除城市"${s.name}"吗？删除后不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await _.deleteCity(s.id),d.success("城市删除成功"),m.value&&await W(m.value),await x()}catch(l){l!=="cancel"&&(console.error("删除城市失败:",l),d.error(l.message||"删除失败"))}},il=async s=>{try{await J.confirm(`确定要删除区县"${s.name}"吗？删除后不可恢复！`,"确确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await _.deleteDistrict(s.id),d.success("区县删除成功"),g.value&&await X(g.value),await x()}catch(l){l!=="cancel"&&(console.error("删除区县失败:",l),d.error(l.message||"删除失败"))}},rl=async()=>{if(T.value.length===0){d.warning("请选择要删除的省份");return}try{await J.confirm(`确定要删除选中的 ${T.value.length} 个省份吗？删除后不可恢复！`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=T.value.map(l=>_.deleteProvince(l.id));await Promise.all(s),d.success(`成功删除 ${T.value.length} 个省份`),T.value=[],await Q(),await x()}catch(s){s!=="cancel"&&(console.error("批量删除省份失败:",s),d.error(s.message||"批量删除失败"))}},ul=async()=>{if(A.value.length===0){d.warning("请选择要删除的城市");return}try{await J.confirm(`确定要删除选中的 ${A.value.length} 个城市吗？删除后不可恢复！`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=A.value.map(l=>_.deleteCity(l.id));await Promise.all(s),d.success(`成功删除 ${A.value.length} 个城市`),A.value=[],m.value&&await W(m.value),await x()}catch(s){s!=="cancel"&&(console.error("批量删除城市失败:",s),d.error(s.message||"批量删除失败"))}},dl=async()=>{if(R.value.length===0){d.warning("请选择要删除的区县");return}try{await J.confirm(`确定要删除选中的 ${R.value.length} 个区县吗？删除后不可恢复！`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=R.value.map(l=>_.deleteDistrict(l.id));await Promise.all(s),d.success(`成功删除 ${R.value.length} 个区县`),R.value=[],g.value&&await X(g.value),await x()}catch(s){s!=="cancel"&&(console.error("批量删除区县失败:",s),d.error(s.message||"批量删除失败"))}},cl=async()=>{if(ve.value)try{await ve.value.validate(),D.value=!0,H.value?(await _.updateProvince(k.value.id,{name:k.value.name}),d.success("省份更新成功")):(await _.addProvince(k.value),d.success("省份添加成功")),N.value=!1,await Q(),await x()}catch(s){console.error("提交省份表单失败:",s),d.error(s.message||"操作失败")}finally{D.value=!1}},vl=async()=>{if(me.value)try{await me.value.validate(),D.value=!0,L.value?(await _.updateCity(h.value.id,{name:h.value.name}),d.success("城市更新成功")):(await _.addCity(h.value),d.success("城市添加成功")),K.value=!1,m.value&&await W(m.value),await x()}catch(s){console.error("提交城市表单失败:",s),d.error(s.message||"操作失败")}finally{D.value=!1}},ml=async()=>{if(pe.value)try{await pe.value.validate(),D.value=!0,q.value?(await _.updateDistrict(w.value.id,{name:w.value.name}),d.success("区县更新成功")):(await _.addDistrict(w.value),d.success("区县添加成功")),M.value=!1,g.value&&await X(g.value),await x()}catch(s){console.error("提交区县表单失败:",s),d.error(s.message||"操作失败")}finally{D.value=!1}},pl=async()=>{ie.value=!0;try{await Promise.all([x(),Q()]),d({message:"地址数据已刷新",type:"success",duration:1500,showClose:!1})}catch(s){console.error("刷新地址数据失败:",s),d.error("刷新地址数据失败")}finally{ie.value=!1}};return Vl(async()=>{console.log("地址管理页面已挂载，开始加载数据...");try{await Promise.all([x(),Q()]),console.log("地址数据加载完成")}catch(s){console.error("地址数据加载失败:",s),d.error("地址数据加载失败: "+s.message)}}),(s,l)=>{const u=Pl,fl=kl,fe=Sl,_l=El,gl=zl("Refresh"),v=Ll,E=Fl,p=Al,$=Rl,_e=Tl,ge=ql,ye=Bl,ee=jl,le=Il,yl=$l,bl=Ul,Cl=Dl,B=Ml,be=Kl,Ce=Nl,hl=xl;return f(),S(hl,{class:"addresses-container"},{default:a(()=>[e(fl,{class:"page-header",height:"auto"},{default:a(()=>[n("div",Hl,[n("div",Ol,[n("h1",Gl,[e(u,{class:"title-icon"},{default:a(()=>[e(r(O))]),_:1}),l[27]||(l[27]=o(" 地址管理 ",-1))]),l[28]||(l[28]=n("p",{class:"page-subtitle"},"管理系统中的省市区地址信息",-1))])])]),_:1}),e(Cl,{class:"main-content"},{default:a(()=>[n("div",Jl,[e(_l,{gutter:16},{default:a(()=>[e(fe,{xs:24,sm:8,md:8},{default:a(()=>[n("div",Yl,[n("div",Zl,[e(u,{class:"stats-icon"},{default:a(()=>[e(r(O))]),_:1}),n("div",Ql,[n("div",Wl,c(P.value.length),1),l[29]||(l[29]=n("div",{class:"stats-label"},"省份总数",-1))])])])]),_:1}),e(fe,{xs:24,sm:8,md:8},{default:a(()=>[n("div",Xl,[n("div",ea,[e(u,{class:"stats-icon"},{default:a(()=>[e(r(oe))]),_:1}),n("div",la,[n("div",aa,c(Fe.value),1),l[30]||(l[30]=n("div",{class:"stats-label"},"城市总数",-1))])])])]),_:1}),e(fe,{xs:24,sm:8,md:8},{default:a(()=>[n("div",ta,[n("div",sa,[e(u,{class:"stats-icon"},{default:a(()=>[e(r(he))]),_:1}),n("div",na,[n("div",oa,c(Te.value),1),l[31]||(l[31]=n("div",{class:"stats-label"},"区县总数",-1))])])])]),_:1})]),_:1})]),e(bl,{class:"content-card",shadow:"never"},{header:a(()=>[n("div",ia,[l[35]||(l[35]=n("span",{class:"card-title"},"地区数据管理",-1)),n("div",ra,[e(v,{onClick:pl,loading:ie.value},{default:a(()=>[e(u,null,{default:a(()=>[e(gl)]),_:1}),l[32]||(l[32]=o(" 刷新 ",-1))]),_:1,__:[32]},8,["loading"]),e(v,{type:"primary",onClick:Xe},{default:a(()=>[e(u,null,{default:a(()=>[e(r(xe))]),_:1}),l[33]||(l[33]=o(" 添加省份 ",-1))]),_:1,__:[33]}),e(v,{type:"danger",disabled:T.value.length===0,onClick:rl},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[34]||(l[34]=o(" 批量删除 ",-1))]),_:1,__:[34]},8,["disabled"])])])]),default:a(()=>[e(yl,{modelValue:ne.value,"onUpdate:modelValue":l[12]||(l[12]=t=>ne.value=t),type:"border-card",class:"address-tabs"},{default:a(()=>[e(ye,{name:"provinces"},{label:a(()=>[n("span",ua,[e(u,null,{default:a(()=>[e(r(O))]),_:1}),l[36]||(l[36]=o(" 省份管理 ",-1))])]),default:a(()=>[n("div",da,[e(E,{modelValue:Z.value,"onUpdate:modelValue":l[0]||(l[0]=t=>Z.value=t),placeholder:"搜索省份名称",style:{width:"300px"},clearable:"",onInput:Ze},{prefix:a(()=>[e(u,null,{default:a(()=>[e(r(we))]),_:1})]),_:1},8,["modelValue"])]),n("div",ca,[e(_e,{data:Ae.value,loading:re.value,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#606266"},onSelectionChange:Ge},{default:a(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{label:"序号",width:"70",align:"center"},{default:a(({$index:t})=>[n("span",va,c(t+1),1)]),_:1}),e(p,{prop:"code",label:"省份代码",width:"120",align:"center"},{default:a(({row:t})=>[e($,{type:"info",size:"small"},{default:a(()=>[o(c(t.code),1)]),_:2},1024)]),_:1}),e(p,{prop:"name",label:"省份名称","min-width":"150"},{default:a(({row:t})=>[n("div",ma,[e(u,{class:"location-icon"},{default:a(()=>[e(r(O))]),_:1}),n("span",pa,c(t.name),1)])]),_:1}),e(p,{prop:"createdAt",label:"创建时间",width:"160",align:"center"},{default:a(({row:t})=>[n("span",fa,c(r(ze)(t.createdAt)),1)]),_:1}),e(p,{label:"操作",width:"240",align:"center"},{default:a(({row:t})=>[n("div",_a,[e(v,{type:"primary",size:"small",plain:"",onClick:b=>qe(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(Se))]),_:1}),l[37]||(l[37]=o(" 查看城市 ",-1))]),_:2,__:[37]},1032,["onClick"]),e(v,{type:"warning",size:"small",plain:"",onClick:b=>al(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(Ve))]),_:1}),l[38]||(l[38]=o(" 编辑 ",-1))]),_:2,__:[38]},1032,["onClick"]),e(v,{type:"danger",size:"small",plain:"",onClick:b=>nl(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[39]||(l[39]=o(" 删除 ",-1))]),_:2,__:[39]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),n("div",ga,[e(ge,{"current-page":V.value.current,"onUpdate:currentPage":l[1]||(l[1]=t=>V.value.current=t),"page-size":V.value.size,"onUpdate:pageSize":l[2]||(l[2]=t=>V.value.size=t),"page-sizes":[10,20,50,100],total:ke.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:je,onCurrentChange:Ne},null,8,["current-page","page-size","total"])])])]),_:1}),e(ye,{name:"cities"},{label:a(()=>[n("span",ya,[e(u,null,{default:a(()=>[e(r(oe))]),_:1}),l[40]||(l[40]=o(" 城市管理 ",-1))])]),default:a(()=>[n("div",ba,[n("div",Ca,[e(le,{modelValue:m.value,"onUpdate:modelValue":l[3]||(l[3]=t=>m.value=t),placeholder:"选择省份",onChange:Ee,style:{width:"200px","margin-right":"12px"},clearable:"",filterable:""},{default:a(()=>[(f(!0),U(ae,null,te(P.value,t=>(f(),S(ee,{key:t.code,label:t.name,value:t.code},{default:a(()=>[n("span",ha,[e(u,null,{default:a(()=>[e(r(O))]),_:1}),o(" "+c(t.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),n("div",wa,[m.value?(f(),S($,{key:0,type:"primary",size:"small"},{default:a(()=>{var t;return[o(" 已选择省份："+c((t=P.value.find(b=>b.code===m.value))==null?void 0:t.name),1)]}),_:1})):(f(),U("span",Va,"请选择省份查看对应城市"))])]),n("div",xa,[e(v,{type:"primary",disabled:!m.value,onClick:el},{default:a(()=>[e(u,null,{default:a(()=>[e(r(xe))]),_:1}),l[41]||(l[41]=o(" 添加城市 ",-1))]),_:1,__:[41]},8,["disabled"]),e(v,{type:"danger",disabled:A.value.length===0,onClick:ul},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[42]||(l[42]=o(" 批量删除 ",-1))]),_:1,__:[42]},8,["disabled"])])]),n("div",za,[e(E,{modelValue:I.value,"onUpdate:modelValue":l[4]||(l[4]=t=>I.value=t),placeholder:"搜索城市名称",style:{width:"300px"},clearable:"",onInput:Qe},{prefix:a(()=>[e(u,null,{default:a(()=>[e(r(we))]),_:1})]),_:1},8,["modelValue"])]),n("div",ka,[e(_e,{data:Re.value,loading:ue.value,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#606266"},"empty-text":"请先选择省份",onSelectionChange:Je},{default:a(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{label:"序号",width:"70",align:"center"},{default:a(({$index:t})=>[n("span",Pa,c((y.value.current-1)*y.value.size+t+1),1)]),_:1}),e(p,{prop:"code",label:"城市代码",width:"120",align:"center"},{default:a(({row:t})=>[e($,{type:"success",size:"small"},{default:a(()=>[o(c(t.code),1)]),_:2},1024)]),_:1}),e(p,{prop:"name",label:"城市名称","min-width":"150"},{default:a(({row:t})=>[n("div",Da,[e(u,{class:"location-icon"},{default:a(()=>[e(r(oe))]),_:1}),n("span",Ea,c(t.name),1)])]),_:1}),e(p,{prop:"provinceCode",label:"所属省份",width:"120",align:"center"},{default:a(({row:t})=>[e($,{type:"info",size:"small"},{default:a(()=>[o(c(t.provinceCode),1)]),_:2},1024)]),_:1}),e(p,{prop:"createdAt",label:"创建时间",width:"140",align:"center"},{default:a(({row:t})=>[n("span",Sa,c(r(ze)(t.createdAt)),1)]),_:1}),e(p,{label:"操作",width:"240",align:"center"},{default:a(({row:t})=>[n("div",Ua,[e(v,{type:"success",size:"small",plain:"",onClick:b=>Ie(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(Se))]),_:1}),l[43]||(l[43]=o(" 查看区县 ",-1))]),_:2,__:[43]},1032,["onClick"]),e(v,{type:"warning",size:"small",plain:"",onClick:b=>tl(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(Ve))]),_:1}),l[44]||(l[44]=o(" 编辑 ",-1))]),_:2,__:[44]},1032,["onClick"]),e(v,{type:"danger",size:"small",plain:"",onClick:b=>ol(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[45]||(l[45]=o(" 删除 ",-1))]),_:2,__:[45]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),z.value.length>0?(f(),U("div",$a,[e(ge,{"current-page":y.value.current,"onUpdate:currentPage":l[5]||(l[5]=t=>y.value.current=t),"page-size":y.value.size,"onUpdate:pageSize":l[6]||(l[6]=t=>y.value.size=t),"page-sizes":[10,20,50,100],total:Pe.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ke,onCurrentChange:Me},null,8,["current-page","page-size","total"])])):se("",!0)])]),_:1}),e(ye,{name:"districts"},{label:a(()=>[n("span",Ba,[e(u,null,{default:a(()=>[e(r(he))]),_:1}),l[46]||(l[46]=o(" 区县管理 ",-1))])]),default:a(()=>[n("div",Fa,[n("div",Ta,[e(le,{modelValue:m.value,"onUpdate:modelValue":l[7]||(l[7]=t=>m.value=t),placeholder:"选择省份",onChange:Ee,style:{width:"200px","margin-right":"12px"},clearable:"",filterable:""},{default:a(()=>[(f(!0),U(ae,null,te(P.value,t=>(f(),S(ee,{key:t.code,label:t.name,value:t.code},{default:a(()=>[n("span",Aa,[e(u,null,{default:a(()=>[e(r(O))]),_:1}),o(" "+c(t.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),e(le,{modelValue:g.value,"onUpdate:modelValue":l[8]||(l[8]=t=>g.value=t),placeholder:"选择城市",onChange:X,style:{width:"200px","margin-right":"12px"},disabled:!m.value,clearable:"",filterable:""},{default:a(()=>[(f(!0),U(ae,null,te(z.value,t=>(f(),S(ee,{key:t.code,label:t.name,value:t.code},{default:a(()=>[n("span",Ra,[e(u,null,{default:a(()=>[e(r(oe))]),_:1}),o(" "+c(t.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),n("div",La,[m.value?(f(),S($,{key:0,type:"primary",size:"small"},{default:a(()=>{var t;return[o(" 省份："+c((t=P.value.find(b=>b.code===m.value))==null?void 0:t.name),1)]}),_:1})):se("",!0),g.value?(f(),S($,{key:1,type:"success",size:"small",style:{"margin-left":"8px"}},{default:a(()=>{var t;return[o(" 城市："+c((t=z.value.find(b=>b.code===g.value))==null?void 0:t.name),1)]}),_:1})):se("",!0),m.value?se("",!0):(f(),U("span",qa,"请先选择省份和城市"))])]),n("div",Ia,[e(v,{type:"primary",disabled:!g.value,onClick:ll},{default:a(()=>[e(u,null,{default:a(()=>[e(r(xe))]),_:1}),l[47]||(l[47]=o(" 添加区县 ",-1))]),_:1,__:[47]},8,["disabled"]),e(v,{type:"danger",disabled:R.value.length===0,onClick:dl},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[48]||(l[48]=o(" 批量删除 ",-1))]),_:1,__:[48]},8,["disabled"])])]),n("div",ja,[e(E,{modelValue:j.value,"onUpdate:modelValue":l[9]||(l[9]=t=>j.value=t),placeholder:"搜索区县名称",style:{width:"300px"},clearable:"",onInput:We},{prefix:a(()=>[e(u,null,{default:a(()=>[e(r(we))]),_:1})]),_:1},8,["modelValue"])]),n("div",Na,[e(_e,{data:Le.value,loading:de.value,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#606266"},"empty-text":"请先选择省份和城市",onSelectionChange:Ye},{default:a(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{prop:"code",label:"区县代码",width:"120",align:"center"},{default:a(({row:t})=>[e($,{type:"warning",size:"small"},{default:a(()=>[o(c(t.code),1)]),_:2},1024)]),_:1}),e(p,{prop:"name",label:"区县名称","min-width":"150"},{default:a(({row:t})=>[n("div",Ka,[e(u,{class:"location-icon"},{default:a(()=>[e(r(he))]),_:1}),n("span",Ma,c(t.name),1)])]),_:1}),e(p,{prop:"cityCode",label:"所属城市",width:"120",align:"center"},{default:a(({row:t})=>[e($,{type:"success",size:"small"},{default:a(()=>[o(c(t.cityCode),1)]),_:2},1024)]),_:1}),e(p,{prop:"createdAt",label:"创建时间",width:"140",align:"center"},{default:a(({row:t})=>[n("span",Ha,c(r(ze)(t.createdAt)),1)]),_:1}),e(p,{label:"操作",width:"160",align:"center"},{default:a(({row:t})=>[n("div",Oa,[e(v,{type:"warning",size:"small",plain:"",onClick:b=>sl(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(Ve))]),_:1}),l[49]||(l[49]=o(" 编辑 ",-1))]),_:2,__:[49]},1032,["onClick"]),e(v,{type:"danger",size:"small",plain:"",onClick:b=>il(t)},{default:a(()=>[e(u,null,{default:a(()=>[e(r(G))]),_:1}),l[50]||(l[50]=o(" 删除 ",-1))]),_:2,__:[50]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),Y.value.length>0?(f(),U("div",Ga,[e(ge,{"current-page":C.value.current,"onUpdate:currentPage":l[10]||(l[10]=t=>C.value.current=t),"page-size":C.value.size,"onUpdate:pageSize":l[11]||(l[11]=t=>C.value.size=t),"page-sizes":[10,20,50,100],total:De.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:He,onCurrentChange:Oe},null,8,["current-page","page-size","total"])])):se("",!0)])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(Ce,{modelValue:N.value,"onUpdate:modelValue":l[16]||(l[16]=t=>N.value=t),title:H.value?"编辑省份":"添加省份",width:"500px"},{footer:a(()=>[e(v,{onClick:l[15]||(l[15]=t=>N.value=!1)},{default:a(()=>l[51]||(l[51]=[o("取消",-1)])),_:1,__:[51]}),e(v,{type:"primary",onClick:cl,loading:D.value},{default:a(()=>[o(c(H.value?"更新":"添加"),1)]),_:1},8,["loading"])]),default:a(()=>[e(be,{ref_key:"provinceFormRef",ref:ve,model:k.value,rules:Ue,"label-width":"80px"},{default:a(()=>[e(B,{label:"省份代码",prop:"code"},{default:a(()=>[e(E,{modelValue:k.value.code,"onUpdate:modelValue":l[13]||(l[13]=t=>k.value.code=t),placeholder:"请输入省份代码",disabled:H.value},null,8,["modelValue","disabled"])]),_:1}),e(B,{label:"省份名称",prop:"name"},{default:a(()=>[e(E,{modelValue:k.value.name,"onUpdate:modelValue":l[14]||(l[14]=t=>k.value.name=t),placeholder:"请输入省份名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(Ce,{modelValue:K.value,"onUpdate:modelValue":l[21]||(l[21]=t=>K.value=t),title:L.value?"编辑城市":"添加城市",width:"500px"},{footer:a(()=>[e(v,{onClick:l[20]||(l[20]=t=>K.value=!1)},{default:a(()=>l[52]||(l[52]=[o("取消",-1)])),_:1,__:[52]}),e(v,{type:"primary",onClick:vl,loading:D.value},{default:a(()=>[o(c(L.value?"更新":"添加"),1)]),_:1},8,["loading"])]),default:a(()=>[e(be,{ref_key:"cityFormRef",ref:me,model:h.value,rules:$e,"label-width":"80px"},{default:a(()=>[e(B,{label:"城市代码",prop:"code"},{default:a(()=>[e(E,{modelValue:h.value.code,"onUpdate:modelValue":l[17]||(l[17]=t=>h.value.code=t),placeholder:"请输入城市代码",disabled:L.value},null,8,["modelValue","disabled"])]),_:1}),e(B,{label:"城市名称",prop:"name"},{default:a(()=>[e(E,{modelValue:h.value.name,"onUpdate:modelValue":l[18]||(l[18]=t=>h.value.name=t),placeholder:"请输入城市名称"},null,8,["modelValue"])]),_:1}),e(B,{label:"所属省份",prop:"provinceCode"},{default:a(()=>[e(le,{modelValue:h.value.provinceCode,"onUpdate:modelValue":l[19]||(l[19]=t=>h.value.provinceCode=t),placeholder:"请选择省份",style:{width:"100%"},disabled:L.value},{default:a(()=>[(f(!0),U(ae,null,te(P.value,t=>(f(),S(ee,{key:t.code,label:t.name,value:t.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(Ce,{modelValue:M.value,"onUpdate:modelValue":l[26]||(l[26]=t=>M.value=t),title:q.value?"编辑区县":"添加区县",width:"500px"},{footer:a(()=>[e(v,{onClick:l[25]||(l[25]=t=>M.value=!1)},{default:a(()=>l[53]||(l[53]=[o("取消",-1)])),_:1,__:[53]}),e(v,{type:"primary",onClick:ml,loading:D.value},{default:a(()=>[o(c(q.value?"更新":"添加"),1)]),_:1},8,["loading"])]),default:a(()=>[e(be,{ref_key:"districtFormRef",ref:pe,model:w.value,rules:Be,"label-width":"80px"},{default:a(()=>[e(B,{label:"区县代码",prop:"code"},{default:a(()=>[e(E,{modelValue:w.value.code,"onUpdate:modelValue":l[22]||(l[22]=t=>w.value.code=t),placeholder:"请输入区县代码",disabled:q.value},null,8,["modelValue","disabled"])]),_:1}),e(B,{label:"区县名称",prop:"name"},{default:a(()=>[e(E,{modelValue:w.value.name,"onUpdate:modelValue":l[23]||(l[23]=t=>w.value.name=t),placeholder:"请输入区县名称"},null,8,["modelValue"])]),_:1}),e(B,{label:"所属城市",prop:"cityCode"},{default:a(()=>[e(le,{modelValue:w.value.cityCode,"onUpdate:modelValue":l[24]||(l[24]=t=>w.value.cityCode=t),placeholder:"请选择城市",style:{width:"100%"},disabled:q.value},{default:a(()=>[(f(!0),U(ae,null,te(z.value,t=>(f(),S(ee,{key:t.code,label:t.name,value:t.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])]),_:1})}}},ct=wl(Ja,[["__scopeId","data-v-4f7c0116"]]);export{ct as default};
