import{_ as st}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                *//* empty css                     *//* empty css                         *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css                 *//* empty css               */import{a as nt,v as lt,g as h,x as R,u as it,r as p,y as A,z as rt,A as re,o as dt,B as C,w as o,C as ct,h as ut,D as de,i as v,d as t,F as mt,b as s,G as $,H as z,I as pt,s as ft,m as c,J as _t,K as vt,L as gt,M as yt,e as bt,N as ht,l as Ft,O as ce,t as w,P as wt,q as b,Q as xt,R as Ct,S as kt,T as St,U as Et,V as ue,W as Nt,X as At,c as S,Y as Tt,Z as O,_ as me,$ as $t,a0 as zt,a1 as Mt,a2 as Wt,a3 as Ut,a4 as pe,a5 as Vt,a6 as fe,a7 as Bt,a8 as Dt,a9 as P,aa as Lt,ab as It,ac as Ot,ad as Pt,ae as Rt,af as qt,ag as Ht,ah as Kt,ai as Gt,aj as _e,ak as Jt,al as jt,am as Qt,an as Xt,ao as Yt,ap as Zt,aq as eo,ar as to}from"./index-DylWFde9.js";const f=nt({notifications:[],unreadCount:0,isConnected:!1,lastOrderCount:0});function oo(){ao(),ve(),ro()}function ao(){"Notification"in window&&Notification.permission==="default"&&Notification.requestPermission().then(n=>{n==="granted"&&console.log("浏览器通知权限已获取")})}function ve(){lt("wss://www.mxm.qiangs.xyz/api/ws",{onConnected:m=>{console.log("WebSocket连接已建立"),f.isConnected=!0,h.success("实时通知已连接")},onDisconnected:(m,u)=>{console.log("WebSocket连接已断开"),f.isConnected=!1,setTimeout(()=>{f.isConnected||ve()},5e3)},onError:(m,u)=>{console.error("WebSocket连接错误:",u),f.isConnected=!1},onMessage:(m,u)=>{try{if(u.data.startsWith("{")||u.data.startsWith("[")){const _=JSON.parse(u.data);so(_)}else console.log("WebSocket文本消息:",u.data),u.data==="连接成功"&&console.log("WebSocket连接已建立")}catch(_){console.error("解析WebSocket消息失败:",_)}},heartbeat:{message:"ping",interval:3e4,pongTimeout:5e3}})}function so(n){switch(n.type){case"NEW_ORDER":ge(n.data);break;case"ORDER_STATUS_CHANGE":no(n.data);break;case"LOW_STOCK":ye(n.data);break;default:console.log("未知消息类型:",n.type)}}function ge(n){const m={id:Date.now(),title:"新订单提醒",description:`订单号：${n.orderNo}，金额：¥${n.totalAmount}`,time:new Date,read:!1,icon:"ShoppingCart",iconColor:"#409EFF",type:"order",data:n};q(m),R({title:"新订单提醒",message:`您有新的订单：${n.orderNo}`,type:"success",duration:5e3,onClick:()=>{window.open(`/#/orders/${n.id}`,"_blank")}}),lo("新订单提醒",`订单号：${n.orderNo}，金额：¥${n.totalAmount}`,"/favicon.ico"),io()}function no(n){const m=mo(n.status),u={id:Date.now(),title:"订单状态更新",description:`订单${n.orderNo}状态已更新为：${m}`,time:new Date,read:!1,icon:"Bell",iconColor:"#E6A23C",type:"order_status",data:n};q(u),R({title:"订单状态更新",message:`订单${n.orderNo}状态已更新为：${m}`,type:"info",duration:3e3})}function ye(n){const m={id:Date.now(),title:"库存预警",description:`${n.flowerName}库存不足，当前库存：${n.stock}`,time:new Date,read:!1,icon:"Warning",iconColor:"#E6A23C",type:"stock",data:n};q(m),R({title:"库存预警",message:`${n.flowerName}库存不足，请及时补货`,type:"warning",duration:0})}function q(n){f.notifications.unshift(n),f.unreadCount++,f.notifications.length>100&&(f.notifications=f.notifications.slice(0,100))}function lo(n,m,u){if("Notification"in window&&Notification.permission==="granted"){const _=new Notification(n,{body:m,icon:u,badge:u,tag:"flower-shop-notification",requireInteraction:!0});_.onclick=()=>{window.focus(),_.close()},setTimeout(()=>{_.close()},5e3)}}function io(){try{const n=new Audio("/notification-sound.mp3");n.volume=.5,n.play().catch(m=>{console.log("播放提示音失败:",m)})}catch(n){console.log("创建音频对象失败:",n)}}function ro(){setInterval(async()=>{},3e4)}function co(n){const m=f.notifications.find(u=>u.id===n);m&&!m.read&&(m.read=!0,f.unreadCount--)}function uo(){f.notifications.forEach(n=>{n.read||(n.read=!0)}),f.unreadCount=0}function mo(n){return{1:"待支付",2:"已支付",3:"配送中",4:"已完成",5:"已取消"}[n]||"未知状态"}function po(){const n={id:999,orderNo:"TEST"+Date.now(),totalAmount:"99.99",userNickname:"测试用户"};ge(n)}function fo(){ye({id:888,flowerName:"测试玫瑰",stock:5,minStock:10})}const _o={class:"header-wrapper"},vo={class:"header-left"},go={class:"brand-section"},yo={class:"brand-info"},bo={class:"header-center"},ho={class:"global-search"},Fo={class:"search-item"},wo={class:"search-item-content"},xo={class:"search-item-title"},Co={class:"search-item-desc"},ko={class:"breadcrumb-nav"},So={class:"header-right"},Eo={class:"quick-actions"},No={class:"notification-panel"},Ao={class:"notification-header"},To={class:"notification-title"},$o={class:"notification-actions"},zo={key:0,class:"empty-notifications"},Mo={key:1},Wo=["onClick"],Uo={class:"notification-icon"},Vo={class:"notification-content"},Bo={class:"notification-text"},Do={class:"notification-desc"},Lo={class:"notification-time"},Io={class:"notification-status"},Oo={class:"notification-footer"},Po={style:{padding:"20px",color:"black"}},Ro={style:{"margin-top":"30px"}},qo={style:{"margin-bottom":"15px",display:"flex","align-items":"center",gap:"10px"}},Ho={style:{"margin-bottom":"15px",display:"flex","align-items":"center",gap:"10px"}},Ko={style:{"margin-bottom":"20px"}},Go={style:{"margin-bottom":"15px"}},Jo={style:{display:"flex",gap:"8px","flex-wrap":"wrap"}},jo=["onClick","title"],Qo={style:{"margin-bottom":"15px"}},Xo={class:"dialog-footer"},Yo={class:"user-profile"},Zo={class:"user-info"},ea={class:"user-name"},ta={class:"sidebar-wrapper"},oa={class:"content-wrapper"},aa={__name:"MainLayout",setup(n){const m=rt(),u=ut(),_=it(),d=p(!1),M=p(""),W=p(Date.now()),H=A(()=>{var e;const a=((e=_.user)==null?void 0:e.avatar)||M.value;return a?a.includes("?t=")?a:a+"?t="+W.value:""}),E=p("");p([]);const K=A(()=>f.notifications),k=p(!1),U=p(!1),F=p("#FFFFFF"),N=p("#FFFFFF"),G=p(!0),J=p(!0),j=p(!0),V=p("fluid"),Q=p("side"),X=p(!0),Y=p(30),Z=p(!0),ee=p(!1),te=p("memory"),B=[{name:"默认白",value:"#FFFFFF"},{name:"默认蓝",value:"#409EFF"},{name:"成功绿",value:"#67C23A"},{name:"警告橙",value:"#E6A23C"},{name:"危险红",value:"#F56C6C"},{name:"信息灰",value:"#909399"},{name:"紫色",value:"#722ED1"},{name:"青色",value:"#13C2C2"},{name:"粉色",value:"#EB2F96"},{name:"深蓝",value:"#1976D2"},{name:"橙色",value:"#FF9800"},{name:"深绿",value:"#388E3C"},{name:"棕色",value:"#795548"}],be=A(()=>d.value?"80px":"260px"),he=A(()=>m.path),oe=A(()=>f.unreadCount),ae=()=>{d.value=!d.value},Fe=()=>({"/":"仪表盘","/users":"用户管理","/flowers":"商品列表","/categories":"分类管理","/price-categories":"价格分类管理","/orders":"订单管理","/reviews":"评价管理","/addresses":"地址管理","/admin-users":"后端用户管理","/profile":"个人设置"})[m.path]||"未知页面",we=()=>{document.fullscreenElement?(document.exitFullscreen(),h.success("已退出全屏模式")):(document.documentElement.requestFullscreen(),h.success("已进入全屏模式"))},xe=(a,e)=>{if(!a){e([]);return}const i=[{title:"商品管理",description:"管理商品信息、库存等",category:"菜单",type:"primary",icon:"Goods",path:"/flowers"},{title:"订单管理",description:"查看和处理订单",category:"菜单",type:"success",icon:"ShoppingCart",path:"/orders"},{title:"用户管理",description:"管理用户信息",category:"菜单",type:"warning",icon:"User",path:"/users"},{title:"分类管理",description:"管理商品分类",category:"菜单",type:"info",icon:"FolderOpened",path:"/categories"}].filter(x=>x.title.toLowerCase().includes(a.toLowerCase())||x.description.toLowerCase().includes(a.toLowerCase()));e(i)},Ce=a=>{a.path&&(u.push(a.path),E.value="")},ke=()=>{E.value.trim()&&h.info(`搜索: ${E.value}`)},Se=a=>{switch(co(a.id),a.type){case"order":a.data&&a.data.id?u.push(`/orders/${a.data.id}`):u.push("/orders");break;case"order_status":a.data&&a.data.id?u.push(`/orders/${a.data.id}`):u.push("/orders");break;case"stock":u.push("/flowers");break}},Ee=()=>{uo(),h.success("所有通知已标记为已读")},Ne=()=>{h.info("通知中心功能开发中")},Ae=a=>{const r=new Date-a,i=Math.floor(r/6e4),x=Math.floor(r/36e5),D=Math.floor(r/864e5);return i<1?"刚刚":i<60?`${i}分钟前`:x<24?`${x}小时前`:`${D}天前`},se=()=>{console.log("打开设置对话框"),k.value=!0,console.log("settingsDrawerVisible:",k.value)},Te=a=>{a&&(F.value=a,N.value=a,T(a),h.success("主题颜色已更新"))},$e=a=>{var e;F.value=a,N.value=a,T(a),h.success(`已应用${((e=B.find(r=>r.value===a))==null?void 0:e.name)||""}主题色`)},T=a=>{document.documentElement.style.setProperty("--el-color-primary",a),document.documentElement.style.setProperty("--el-color-primary-light-3",a+"4D"),document.documentElement.style.setProperty("--el-color-primary-light-5",a+"80"),document.documentElement.style.setProperty("--el-color-primary-light-7",a+"B3"),document.documentElement.style.setProperty("--el-color-primary-light-8",a+"CC"),document.documentElement.style.setProperty("--el-color-primary-light-9",a+"E6"),document.documentElement.style.setProperty("--el-color-primary-dark-2",a);const e=document.querySelector(".modern-header"),r=document.querySelector(".modern-sidebar"),i=a.toUpperCase()==="#FFFFFF"||a.toUpperCase()==="#FFF";e&&(i?(e.style.background="linear-gradient(135deg, #FFFFFF 0%, #F5F5F5 100%)",e.style.borderBottom="1px solid #E0E0E0",e.style.color="#333333"):(e.style.background=`linear-gradient(135deg, ${a} 0%, ${a}CC 100%)`,e.style.borderBottom="none",e.style.color="#FFFFFF")),r&&(i?(r.style.background="linear-gradient(180deg, #FFFFFF 0%, #FAFAFA 100%)",r.style.borderRight="1px solid #E0E0E0",r.style.color="#333333"):(r.style.background=`linear-gradient(180deg, ${a} 0%, ${a}E6 100%)`,r.style.borderRight="none",r.style.color="#FFFFFF"))},ze=()=>{const a="#FFFFFF";F.value=a,N.value=a,T(a),h.success("已重置为默认主题色")},Me=()=>{const a={isDarkMode:U.value,currentThemeColor:F.value,customThemeColor:N.value,sidebarCollapsed:d.value,fixedHeader:G.value,pageAnimation:J.value,notificationSound:j.value,pageWidth:V.value,navStyle:Q.value,autoSave:X.value,refreshInterval:Y.value,lazyLoading:Z.value,virtualScroll:ee.value,cacheStrategy:te.value};localStorage.setItem("adminSettings",JSON.stringify(a)),h.success("设置已保存"),k.value=!1},We=a=>{console.error("顶部导航栏头像加载失败:",a),console.log("头像URL:",H.value),W.value=Date.now()},Ue=async()=>{var a,e;try{(a=_.user)!=null&&a.avatar,M.value=((e=_.user)==null?void 0:e.avatar)||""}catch(r){console.error("初始化用户头像失败:",r)}},Ve=async a=>{switch(a){case"profile":u.push("/profile");break;case"settings":se();break;case"logout":try{await to.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await _.logout(),u.push("/login"),h.success("退出登录成功")}catch{}break}},Be=()=>{const a=localStorage.getItem("adminSettings");if(a)try{const e=JSON.parse(a);U.value=e.isDarkMode||!1,F.value=e.currentThemeColor||"#FFFFFF",N.value=e.customThemeColor||"#FFFFFF",d.value=e.sidebarCollapsed||!1,G.value=e.fixedHeader!==void 0?e.fixedHeader:!0,J.value=e.pageAnimation!==void 0?e.pageAnimation:!0,j.value=e.notificationSound!==void 0?e.notificationSound:!0,V.value=e.pageWidth||"fluid",Q.value=e.navStyle||"side",X.value=e.autoSave!==void 0?e.autoSave:!0,Y.value=e.refreshInterval||30,Z.value=e.lazyLoading!==void 0?e.lazyLoading:!0,ee.value=e.virtualScroll||!1,te.value=e.cacheStrategy||"memory",document.documentElement.classList.toggle("dark",U.value),T(F.value),De()}catch(e){console.error("加载设置失败:",e)}},De=()=>{const a=document.querySelector(".main-container");a&&(V.value==="fixed"?(a.style.maxWidth="1200px",a.style.margin="0 auto"):(a.style.maxWidth="none",a.style.margin="0"))};re(()=>m.meta.title,a=>{a&&(document.title=`${a} - 花语小铺管理后台`)},{immediate:!0}),dt(()=>{Be(),Ue(),oo()});const Le=()=>{po()},Ie=()=>{fo()};return re(()=>{var a;return(a=_.user)==null?void 0:a.avatar},a=>{a&&(M.value=a,W.value=Date.now())},{immediate:!0}),(a,e)=>{const r=ft,i=Ft,x=wt,D=yt,ne=Ct,Oe=xt,L=Et,g=$t,Pe=Tt,le=At,Re=St,qe=Wt,He=Ut,Ke=Mt,Ge=Dt,I=Ot,Je=It,je=Bt,Qe=mt,y=Ht,Xe=Gt,Ye=de("UserFilled"),Ze=qt,et=Rt,tt=de("router-view"),ot=eo,ie=ct;return v(),C(ie,{class:"modern-layout"},{default:o(()=>[t(Qe,{class:"modern-header",height:"72px"},{default:o(()=>[s("div",_o,[s("div",vo,[s("div",go,[s("div",{class:z(["brand-logo",{collapsed:d.value}])},[e[6]||(e[6]=s("div",{class:"logo-icon"},"🌸",-1)),d.value?(v(),C(r,{key:0,class:"sidebar-toggle-btn collapsed-btn",onClick:ae,icon:c(_t),circle:"",size:"small"},null,8,["icon"])):$("",!0),pt(s("div",yo,e[5]||(e[5]=[s("h1",{class:"brand-title"},"花语小铺",-1),s("span",{class:"brand-subtitle"},"管理后台",-1)]),512),[[vt,!d.value]])],2),d.value?$("",!0):(v(),C(r,{key:0,class:"sidebar-toggle-btn",onClick:ae,icon:c(gt),circle:"",size:"large"},null,8,["icon"]))])]),s("div",bo,[s("div",ho,[t(D,{modelValue:E.value,"onUpdate:modelValue":e[0]||(e[0]=l=>E.value=l),"fetch-suggestions":xe,placeholder:"全局搜索...","prefix-icon":c(ht),class:"search-input",clearable:"",onSelect:Ce,onKeyup:bt(ke,["enter"]),"popper-class":"global-search-popper"},{default:o(({item:l})=>[s("div",Fo,[t(i,{class:"search-item-icon"},{default:o(()=>[(v(),C(ce(l.icon)))]),_:2},1024),s("div",wo,[s("div",xo,w(l.title),1),s("div",Co,w(l.description),1)]),t(x,{size:"small",type:l.type},{default:o(()=>[b(w(l.category),1)]),_:2},1032,["type"])])]),_:1},8,["modelValue","prefix-icon"])]),s("div",ko,[t(Oe,{separator:"/"},{default:o(()=>[t(ne,{to:{path:"/"}},{default:o(()=>[t(i,null,{default:o(()=>[t(c(kt))]),_:1}),e[7]||(e[7]=b(" 首页 ",-1))]),_:1,__:[7]}),t(ne,null,{default:o(()=>[b(w(Fe()),1)]),_:1})]),_:1})])]),s("div",So,[s("div",Eo,[t(Re,{placement:"bottom",width:380,trigger:"click","popper-class":"notification-popover"},{reference:o(()=>[t(g,{content:"消息通知",placement:"bottom"},{default:o(()=>[t(L,{value:oe.value,max:99,class:"notification-badge"},{default:o(()=>[t(r,{class:"action-button",circle:""},{default:o(()=>[t(i,null,{default:o(()=>[t(c(ue))]),_:1})]),_:1})]),_:1},8,["value"])]),_:1})]),default:o(()=>[s("div",No,[s("div",Ao,[s("div",To,[e[8]||(e[8]=s("span",null,"消息通知",-1)),t(L,{value:oe.value,max:99},null,8,["value"]),t(x,{type:c(f).isConnected?"success":"danger",size:"small",style:{"margin-left":"8px"}},{default:o(()=>[b(w(c(f).isConnected?"已连接":"未连接"),1)]),_:1},8,["type"])]),s("div",$o,[t(r,{type:"primary",text:"",size:"small",onClick:Le},{default:o(()=>[t(i,null,{default:o(()=>[t(c(ue))]),_:1}),e[9]||(e[9]=b(" 测试订单 ",-1))]),_:1,__:[9]}),t(r,{type:"warning",text:"",size:"small",onClick:Ie},{default:o(()=>[t(i,null,{default:o(()=>[t(c(Nt))]),_:1}),e[10]||(e[10]=b(" 测试库存 ",-1))]),_:1,__:[10]}),t(r,{text:"",size:"small",onClick:Ee},{default:o(()=>e[11]||(e[11]=[b("全部已读",-1)])),_:1,__:[11]}),t(r,{text:"",size:"small",onClick:a.clearAllNotifications},{default:o(()=>e[12]||(e[12]=[b("清空",-1)])),_:1,__:[12]},8,["onClick"])])]),t(le,{height:"300px",class:"notification-list"},{default:o(()=>[K.value.length===0?(v(),S("div",zo,[t(Pe,{description:"暂无通知","image-size":80})])):(v(),S("div",Mo,[(v(!0),S(O,null,me(K.value,l=>(v(),S("div",{key:l.id,class:z(["notification-item",{unread:!l.read}]),onClick:at=>Se(l)},[s("div",Uo,[t(i,{color:l.iconColor},{default:o(()=>[(v(),C(ce(l.icon)))]),_:2},1032,["color"])]),s("div",Vo,[s("div",Bo,w(l.title),1),s("div",Do,w(l.description),1),s("div",Lo,w(Ae(l.time)),1)]),s("div",Io,[l.read?$("",!0):(v(),C(L,{key:0,"is-dot":""}))])],10,Wo))),128))]))]),_:1}),s("div",Oo,[t(r,{text:"",onClick:Ne},{default:o(()=>e[13]||(e[13]=[b("查看全部通知",-1)])),_:1,__:[13]})])])]),_:1}),t(g,{content:"全屏显示",placement:"bottom"},{default:o(()=>[t(r,{class:"action-button",circle:"",onClick:we},{default:o(()=>[t(i,null,{default:o(()=>[t(c(zt))]),_:1})]),_:1})]),_:1}),t(Ke,{modelValue:k.value,"onUpdate:modelValue":e[4]||(e[4]=l=>k.value=l),title:"系统设置",width:"600px",modal:!0,"close-on-click-modal":!1,"destroy-on-close":!1,class:"settings-dialog","z-index":3e3,"append-to-body":!0},{footer:o(()=>[s("span",Xo,[t(r,{onClick:e[3]||(e[3]=l=>k.value=!1)},{default:o(()=>e[22]||(e[22]=[b("取消",-1)])),_:1,__:[22]}),t(r,{type:"primary",onClick:Me},{default:o(()=>e[23]||(e[23]=[b("保存设置",-1)])),_:1,__:[23]})])]),default:o(()=>[s("div",Po,[e[21]||(e[21]=s("h3",{style:{color:"#333","margin-bottom":"20px"}},"系统设置",-1)),s("div",Ro,[e[20]||(e[20]=s("h4",{style:{color:"#333","margin-bottom":"15px"}},"主题设置",-1)),s("div",qo,[e[14]||(e[14]=s("span",{style:{color:"#666"}},"主题颜色：",-1)),t(qe,{modelValue:F.value,"onUpdate:modelValue":e[1]||(e[1]=l=>F.value=l),onChange:Te,"show-alpha":"",predefine:B.map(l=>l.value),size:"large"},null,8,["modelValue","predefine"]),e[15]||(e[15]=s("span",{style:{color:"#999","font-size":"12px"}},"选择任意颜色",-1))]),s("div",Ho,[e[16]||(e[16]=s("span",{style:{color:"#666"}},"侧边栏折叠：",-1)),t(He,{modelValue:d.value,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value=l),"active-text":"折叠","inactive-text":"展开",size:"large"},null,8,["modelValue"])]),s("div",Ko,[e[19]||(e[19]=s("h4",{style:{color:"#333","margin-bottom":"15px"}},"主题颜色",-1)),s("div",Go,[e[17]||(e[17]=s("span",{style:{color:"#666",display:"block","margin-bottom":"8px"}},"快速选择：",-1)),s("div",Jo,[(v(),S(O,null,me(B,l=>s("div",{key:l.name,onClick:at=>$e(l.value),style:pe({width:"32px",height:"32px",backgroundColor:l.value,borderRadius:"4px",cursor:"pointer",border:F.value===l.value?"3px solid #409EFF":l.value.toUpperCase()==="#FFFFFF"?"2px solid #ccc":"2px solid #ddd",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:l.value.toUpperCase()==="#FFFFFF"?"0 1px 3px rgba(0,0,0,0.1)":"none"}),title:l.name},[F.value===l.value?(v(),C(i,{key:0,style:pe({color:l.value.toUpperCase()==="#FFFFFF"?"#333333":"white",fontSize:"16px"})},{default:o(()=>[t(c(Vt))]),_:2},1032,["style"])):$("",!0)],12,jo)),64))])]),s("div",Qo,[t(r,{size:"small",onClick:ze},{default:o(()=>e[18]||(e[18]=[b("重置为默认颜色",-1)])),_:1,__:[18]})])])])])]),_:1},8,["modelValue"]),t(g,{content:"系统设置",placement:"bottom"},{default:o(()=>[t(r,{class:z(["action-button",{"settings-active":k.value}]),circle:"",onClick:se},{default:o(()=>[t(i,null,{default:o(()=>[t(c(fe))]),_:1})]),_:1},8,["class"])]),_:1})]),t(je,{onCommand:Ve,class:"user-dropdown",trigger:"click"},{dropdown:o(()=>[t(Je,{class:"modern-dropdown-menu"},{default:o(()=>[t(I,{command:"profile",class:"dropdown-item"},{default:o(()=>[t(i,null,{default:o(()=>[t(c(P))]),_:1}),e[25]||(e[25]=s("span",null,"个人设置",-1))]),_:1,__:[25]}),t(I,{command:"settings",class:"dropdown-item"},{default:o(()=>[t(i,null,{default:o(()=>[t(c(fe))]),_:1}),e[26]||(e[26]=s("span",null,"系统设置",-1))]),_:1,__:[26]}),t(I,{command:"logout",divided:"",class:"dropdown-item logout-item"},{default:o(()=>[t(i,null,{default:o(()=>[t(c(Pt))]),_:1}),e[27]||(e[27]=s("span",null,"退出登录",-1))]),_:1,__:[27]})]),_:1})]),default:o(()=>{var l;return[s("div",Yo,[t(Ge,{size:40,src:H.value,class:"user-avatar",onError:We},{default:o(()=>[t(i,null,{default:o(()=>[t(c(P))]),_:1})]),_:1},8,["src"]),s("div",Zo,[s("div",ea,w(((l=c(_).user)==null?void 0:l.username)||"管理员"),1),e[24]||(e[24]=s("div",{class:"user-role"},"超级管理员",-1))]),t(i,{class:"dropdown-arrow"},{default:o(()=>[t(c(Lt))]),_:1})])]}),_:1})])])]),_:1}),t(ie,{class:"main-container"},{default:o(()=>[t(et,{class:z(["modern-sidebar",{collapsed:d.value}]),width:be.value},{default:o(()=>[s("div",ta,[t(le,{class:"sidebar-scrollbar"},{default:o(()=>[t(Ze,{"default-active":he.value,collapse:d.value,"unique-opened":!0,router:"",class:"navigation-menu","background-color":"transparent","text-color":"#64748b","active-text-color":"#3b82f6"},{default:o(()=>[t(g,{content:d.value?"仪表盘":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Kt))]),_:1}),e[28]||(e[28]=s("span",{class:"menu-text"},"仪表盘",-1))]),_:1,__:[28]})]),_:1},8,["content","disabled"]),t(g,{content:d.value?"用户管理":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/users",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(P))]),_:1}),e[29]||(e[29]=s("span",{class:"menu-text"},"用户管理",-1))]),_:1,__:[29]})]),_:1},8,["content","disabled"]),d.value?(v(),S(O,{key:1},[t(g,{content:"商品列表",placement:"right"},{default:o(()=>[t(y,{index:"/flowers",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(_e))]),_:1}),e[34]||(e[34]=s("span",{class:"menu-text"},"商品列表",-1))]),_:1,__:[34]})]),_:1}),t(g,{content:"分类管理",placement:"right"},{default:o(()=>[t(y,{index:"/categories",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Jt))]),_:1}),e[35]||(e[35]=s("span",{class:"menu-text"},"分类管理",-1))]),_:1,__:[35]})]),_:1}),t(g,{content:"价格分类",placement:"right"},{default:o(()=>[t(y,{index:"/price-categories",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(jt))]),_:1}),e[36]||(e[36]=s("span",{class:"menu-text"},"价格分类",-1))]),_:1,__:[36]})]),_:1})],64)):(v(),C(Xe,{key:0,index:"products",class:"nav-submenu"},{title:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(_e))]),_:1}),e[30]||(e[30]=s("span",{class:"menu-text"},"商品管理",-1))]),default:o(()=>[t(y,{index:"/flowers",class:"sub-nav-item"},{default:o(()=>e[31]||(e[31]=[s("span",{class:"submenu-text"},"商品列表",-1)])),_:1,__:[31]}),t(y,{index:"/categories",class:"sub-nav-item"},{default:o(()=>e[32]||(e[32]=[s("span",{class:"submenu-text"},"分类管理",-1)])),_:1,__:[32]}),t(y,{index:"/price-categories",class:"sub-nav-item"},{default:o(()=>e[33]||(e[33]=[s("span",{class:"submenu-text"},"价格分类",-1)])),_:1,__:[33]})]),_:1})),t(g,{content:d.value?"订单管理":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/orders",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Qt))]),_:1}),e[37]||(e[37]=s("span",{class:"menu-text"},"订单管理",-1))]),_:1,__:[37]})]),_:1},8,["content","disabled"]),t(g,{content:d.value?"评价管理":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/reviews",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Xt))]),_:1}),e[38]||(e[38]=s("span",{class:"menu-text"},"评价管理",-1))]),_:1,__:[38]})]),_:1},8,["content","disabled"]),t(g,{content:d.value?"地址管理":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/addresses",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Yt))]),_:1}),e[39]||(e[39]=s("span",{class:"menu-text"},"地址管理",-1))]),_:1,__:[39]})]),_:1},8,["content","disabled"]),t(g,{content:d.value?"后端用户":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/admin-users",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(Ye)]),_:1}),e[40]||(e[40]=s("span",{class:"menu-text"},"后端用户",-1))]),_:1,__:[40]})]),_:1},8,["content","disabled"]),t(g,{content:d.value?"轮播图管理":"",placement:"right",disabled:!d.value},{default:o(()=>[t(y,{index:"/swiper",class:"nav-item"},{default:o(()=>[t(i,{class:"menu-icon"},{default:o(()=>[t(c(Zt))]),_:1}),e[41]||(e[41]=s("span",{class:"menu-text"},"轮播图管理",-1))]),_:1,__:[41]})]),_:1},8,["content","disabled"])]),_:1},8,["default-active","collapse"])]),_:1})])]),_:1},8,["class","width"]),t(ot,{class:"modern-main"},{default:o(()=>[s("div",oa,[t(tt)])]),_:1})]),_:1})]),_:1})}}},va=st(aa,[["__scopeId","data-v-56e663de"]]);export{va as default};
