# 花语小铺 - Vue 3 管理后台

这是花语小铺微信小程序的 Vue 3 管理后台系统，用于管理小程序的用户、商品、订单等数据。

## 功能特性

### 🎯 核心功能
- **仪表盘**: 数据统计概览、图表展示、最近订单
- **用户管理**: 查看用户信息、用户状态管理、用户详情
- **商品管理**: 商品CRUD操作、库存管理、状态控制
- **分类管理**: 商品分类管理、图片上传
- **订单管理**: 订单查看、状态更新、订单详情
- **评价管理**: 用户评价审核、状态管理
- **地址管理**: 配送地区管理

### 🔐 认证授权
- JWT Token 认证
- 管理员登录/登出
- 路由权限控制
- Token 自动刷新

### 🎨 界面设计
- 响应式布局设计
- Element Plus UI 组件库
- 深色/浅色主题支持
- 移动端适配

## 技术栈

- **Vue 3**: 渐进式 JavaScript 框架
- **Vite**: 现代化构建工具
- **Vue Router 4**: 官方路由管理器
- **Pinia**: 状态管理库
- **Element Plus**: Vue 3 UI 组件库
- **Axios**: HTTP 客户端
- **ECharts**: 数据可视化图表库
- **Day.js**: 日期处理库

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 进入 Vue 目录
cd Vue

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

访问 http://localhost:3000 查看管理后台

### 生产构建

```bash
# 构建生产版本
npm run build
# 或使用 yarn
yarn build
```

### 预览生产构建

```bash
# 预览生产构建
npm run preview
# 或使用 yarn
yarn preview
```

## 项目结构

```
Vue/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── admin.js       # 管理员相关接口
│   │   └── request.js     # HTTP 请求配置
│   ├── components/        # 公共组件
│   ├── layouts/           # 布局组件
│   │   └── MainLayout.vue # 主布局
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── stores/            # 状态管理
│   │   └── auth.js        # 认证状态
│   ├── styles/            # 样式文件
│   │   └── index.css      # 全局样式
│   ├── utils/             # 工具函数
│   │   └── index.js       # 通用工具
│   ├── views/             # 页面组件
│   │   ├── Dashboard.vue  # 仪表盘
│   │   ├── Login.vue      # 登录页
│   │   ├── Users.vue      # 用户管理
│   │   ├── Flowers.vue    # 商品管理
│   │   ├── Categories.vue # 分类管理
│   │   ├── Orders.vue     # 订单管理
│   │   └── ...
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 配置说明

### API 代理配置

在 `vite.config.js` 中配置了 API 代理：

```javascript
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

### 环境变量

可以创建 `.env.local` 文件配置环境变量：

```bash
# API 基础地址
VITE_API_BASE_URL=https://mxm.qiangs.xyz:8080/api

# 应用标题
VITE_APP_TITLE=花语小铺管理后台
```

## 默认账号

- 用户名: `admin`
- 密码: `123456`

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在 `src/layouts/MainLayout.vue` 中添加菜单项

### API 接口

所有 API 接口都在 `src/api/admin.js` 中定义，使用统一的请求拦截器处理认证和错误。

### 状态管理

使用 Pinia 进行状态管理，认证相关状态在 `src/stores/auth.js` 中管理。

### 样式规范

- 使用 Element Plus 的设计规范
- 全局样式定义在 `src/styles/index.css`
- 组件样式使用 scoped 作用域

## 部署

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 常见问题

### 1. 登录后页面空白

检查后端服务是否正常运行，确保 API 接口可以正常访问。

### 2. 图片上传失败

确保后端已实现文件上传接口 `/api/admin/upload/image`。

### 3. 路由跳转异常

检查路由配置是否正确，确保所有页面组件都已正确导入。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
