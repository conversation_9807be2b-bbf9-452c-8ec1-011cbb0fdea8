import{_ as re}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                  *//* empty css               *//* empty css                    *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                *//* empty css               *//* empty css                  */import{u as ne,r as u,y as z,a as C,o as ie,c as O,b as s,d as a,w as o,aC as ue,a1 as de,f as U,g as n,i as D,aE as me,aI as pe,a8 as ce,m as g,l as fe,a9 as ve,s as ge,br as _e,t as _,P as we,q as w,bl as be,bm as ye,E as Ve,j as Pe,k as Ue,a3 as Ee,aQ as Le,B as he,aO as Ne}from"./index-Dzimn2-8.js";import{b as G}from"./index-CviIdblf.js";const ke={class:"profile-container"},Ae={class:"profile-avatar-section"},Re={class:"avatar-container"},Fe={class:"profile-info"},xe={class:"profile-name"},Ce={class:"profile-role"},De={class:"profile-stats"},Te={class:"stat-item"},Be={class:"stat-value"},Ie={class:"stat-item"},Se={class:"stat-value"},je={class:"stat-item"},qe={class:"stat-value"},Me={class:"security-settings"},ze={class:"security-item"},Oe={class:"security-item"},Ge={class:"security-item"},$e=["src"],Je={__name:"Profile",setup(Qe){const T=ne(),B=u("basic"),E=u(!1),L=u(!1),h=u(!1),b=u(!1),y=u(""),V=u(null),P=u(Date.now()),I=z(()=>r.value.avatar?r.value.avatar.includes("?t=")?r.value.avatar:r.value.avatar+"?t="+P.value:""),r=u({id:1,username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",avatar:"",role:"super",status:1,lastLoginTime:new Date,createdAt:new Date}),$=u({totalLogins:156,lastLoginIp:"127.0.0.1"}),i=C({username:"",realName:"",email:"",phone:""}),d=C({currentPassword:"",newPassword:"",confirmPassword:""}),v=C({loginProtection:!1,operationLog:!0,autoLogout:!0}),N=u(),k=u();u("http://localhost:8080/api/admin/upload/avatar"),z(()=>({Authorization:`Bearer ${T.token}`}));const J={realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}]},Q={currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(l,e,c)=>{e!==d.newPassword?c(new Error("两次输入的密码不一致")):c()},trigger:"blur"}]},H=l=>({super:"超级管理员",admin:"管理员",editor:"编辑员"})[l]||"未知角色",S=async()=>{try{const l=await U.getProfile();l.code===200?(r.value=l.data,Object.assign(i,{username:l.data.username,realName:l.data.realName||"",email:l.data.email||"",phone:l.data.phone||""})):n.error(l.message||"加载用户信息失败")}catch(l){console.error("加载用户信息失败:",l),n.error("加载用户信息失败")}},K=async()=>{if(N.value)try{await N.value.validate(),E.value=!0;const l=await U.updateProfile({realName:i.realName,email:i.email,phone:i.phone});l.code===200?(Object.assign(r.value,i),n.success("基本信息更新成功")):n.error(l.message||"更新基本信息失败")}catch(l){console.error("更新基本信息失败:",l),l!=="validation failed"&&n.error("更新基本信息失败")}finally{E.value=!1}},W=async()=>{if(k.value)try{await k.value.validate(),L.value=!0;const l=await U.updatePassword({currentPassword:d.currentPassword,newPassword:d.newPassword});l.code===200?(Object.assign(d,{currentPassword:"",newPassword:"",confirmPassword:""}),n.success("密码修改成功")):n.error(l.message||"密码修改失败")}catch(l){console.error("修改密码失败:",l),l!=="validation failed"&&n.error("修改密码失败")}finally{L.value=!1}},X=async()=>{try{h.value=!0,await new Promise(l=>setTimeout(l,1e3)),n.success("安全设置更新成功")}catch(l){console.error("更新安全设置失败:",l),n.error("更新安全设置失败")}finally{h.value=!1}},Y=()=>{b.value=!0,y.value=r.value.avatar,V.value=null},Z=l=>{const e=l.raw,c=e.type==="image/jpeg"||e.type==="image/png",A=e.size/1024/1024<2;if(!c)return n.error("头像只能是 JPG/PNG 格式!"),!1;if(!A)return n.error("头像大小不能超过 2MB!"),!1;V.value=e;const m=new FileReader;m.onload=R=>{y.value=R.target.result},m.readAsDataURL(e)},ee=async()=>{if(!V.value){n.error("请先选择头像文件");return}try{const l=await U.uploadAvatar(V.value);if(console.log("头像上传响应:",l),l.code===200){const e=l.data.url;console.log("新头像URL:",e),r.value.avatar=e,P.value=Date.now(),T.updateUser({avatar:e}),console.log("已更新AuthStore中的头像:",e),b.value=!1,V.value=null,y.value="",n.success("头像更新成功"),setTimeout(async()=>{await S(),P.value=Date.now()},500)}else n.error(l.message||"头像上传失败")}catch(l){console.error("头像上传失败:",l),n.error("头像上传失败")}},ae=l=>{console.error("头像加载失败:",l),console.log("头像URL:",r.value.avatar),console.log("完整头像URL:",I.value),P.value=Date.now()};return ie(()=>{S()}),(l,e)=>{const c=fe,A=ce,m=ge,R=we,j=pe,q=me,f=Ue,p=Pe,M=Ve,F=ye,x=Ee,le=be,oe=ue,se=Le,te=de;return D(),O("div",ke,[e[24]||(e[24]=s("div",{class:"profile-header"},[s("h2",{class:"page-title"},"个人设置"),s("p",{class:"page-desc"},"管理您的个人信息和账户设置")],-1)),a(oe,{gutter:24},{default:o(()=>[a(q,{xs:24,sm:24,md:8,lg:8},{default:o(()=>[a(j,{class:"profile-card"},{default:o(()=>[s("div",Ae,[s("div",Re,[a(A,{size:120,src:g(I),class:"profile-avatar",onError:ae},{default:o(()=>[a(c,{size:"60"},{default:o(()=>[a(g(ve))]),_:1})]),_:1},8,["src"]),a(m,{class:"avatar-upload-btn",circle:"",onClick:Y},{default:o(()=>[a(c,null,{default:o(()=>[a(g(_e))]),_:1})]),_:1})]),s("div",Fe,[s("h3",xe,_(r.value.realName||r.value.username),1),s("p",Ce,_(H(r.value.role)),1),a(R,{type:r.value.status===1?"success":"danger",size:"small"},{default:o(()=>[w(_(r.value.status===1?"正常":"禁用"),1)]),_:1},8,["type"])])]),s("div",De,[s("div",Te,[s("div",Be,_($.value.totalLogins),1),e[13]||(e[13]=s("div",{class:"stat-label"},"登录次数",-1))]),s("div",Ie,[s("div",Se,_(g(G)(r.value.lastLoginTime)),1),e[14]||(e[14]=s("div",{class:"stat-label"},"最后登录",-1))]),s("div",je,[s("div",qe,_(g(G)(r.value.createdAt)),1),e[15]||(e[15]=s("div",{class:"stat-label"},"注册时间",-1))])])]),_:1})]),_:1}),a(q,{xs:24,sm:24,md:16,lg:16},{default:o(()=>[a(j,{class:"settings-card"},{default:o(()=>[a(le,{modelValue:B.value,"onUpdate:modelValue":e[10]||(e[10]=t=>B.value=t),class:"profile-tabs"},{default:o(()=>[a(F,{label:"基本信息",name:"basic"},{default:o(()=>[a(M,{ref_key:"basicFormRef",ref:N,model:i,rules:J,"label-width":"100px",class:"profile-form"},{default:o(()=>[a(p,{label:"用户名",prop:"username"},{default:o(()=>[a(f,{modelValue:i.username,"onUpdate:modelValue":e[0]||(e[0]=t=>i.username=t),disabled:""},null,8,["modelValue"])]),_:1}),a(p,{label:"真实姓名",prop:"realName"},{default:o(()=>[a(f,{modelValue:i.realName,"onUpdate:modelValue":e[1]||(e[1]=t=>i.realName=t),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),a(p,{label:"邮箱",prop:"email"},{default:o(()=>[a(f,{modelValue:i.email,"onUpdate:modelValue":e[2]||(e[2]=t=>i.email=t),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),a(p,{label:"手机号",prop:"phone"},{default:o(()=>[a(f,{modelValue:i.phone,"onUpdate:modelValue":e[3]||(e[3]=t=>i.phone=t),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),a(p,null,{default:o(()=>[a(m,{type:"primary",onClick:K,loading:E.value},{default:o(()=>e[16]||(e[16]=[w(" 保存基本信息 ",-1)])),_:1,__:[16]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),a(F,{label:"修改密码",name:"password"},{default:o(()=>[a(M,{ref_key:"passwordFormRef",ref:k,model:d,rules:Q,"label-width":"100px",class:"profile-form"},{default:o(()=>[a(p,{label:"当前密码",prop:"currentPassword"},{default:o(()=>[a(f,{modelValue:d.currentPassword,"onUpdate:modelValue":e[4]||(e[4]=t=>d.currentPassword=t),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),a(p,{label:"新密码",prop:"newPassword"},{default:o(()=>[a(f,{modelValue:d.newPassword,"onUpdate:modelValue":e[5]||(e[5]=t=>d.newPassword=t),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(p,{label:"确认密码",prop:"confirmPassword"},{default:o(()=>[a(f,{modelValue:d.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=t=>d.confirmPassword=t),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(p,null,{default:o(()=>[a(m,{type:"primary",onClick:W,loading:L.value},{default:o(()=>e[17]||(e[17]=[w(" 修改密码 ",-1)])),_:1,__:[17]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),a(F,{label:"安全设置",name:"security"},{default:o(()=>[s("div",Me,[s("div",ze,[e[18]||(e[18]=s("div",{class:"security-info"},[s("h4",null,"登录保护"),s("p",null,"开启后需要验证码才能登录")],-1)),a(x,{modelValue:v.loginProtection,"onUpdate:modelValue":e[7]||(e[7]=t=>v.loginProtection=t)},null,8,["modelValue"])]),s("div",Oe,[e[19]||(e[19]=s("div",{class:"security-info"},[s("h4",null,"操作日志"),s("p",null,"记录重要操作的日志信息")],-1)),a(x,{modelValue:v.operationLog,"onUpdate:modelValue":e[8]||(e[8]=t=>v.operationLog=t)},null,8,["modelValue"])]),s("div",Ge,[e[20]||(e[20]=s("div",{class:"security-info"},[s("h4",null,"自动登出"),s("p",null,"长时间无操作自动退出登录")],-1)),a(x,{modelValue:v.autoLogout,"onUpdate:modelValue":e[9]||(e[9]=t=>v.autoLogout=t)},null,8,["modelValue"])]),a(m,{type:"primary",onClick:X,loading:h.value},{default:o(()=>e[21]||(e[21]=[w(" 保存安全设置 ",-1)])),_:1,__:[21]},8,["loading"])])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(te,{modelValue:b.value,"onUpdate:modelValue":e[12]||(e[12]=t=>b.value=t),title:"上传头像",width:"400px"},{footer:o(()=>[a(m,{onClick:e[11]||(e[11]=t=>b.value=!1)},{default:o(()=>e[22]||(e[22]=[w("取消",-1)])),_:1,__:[22]}),a(m,{type:"primary",onClick:ee},{default:o(()=>e[23]||(e[23]=[w("确定",-1)])),_:1,__:[23]})]),default:o(()=>[a(se,{class:"avatar-uploader","auto-upload":!1,"show-file-list":!1,"on-change":Z,accept:"image/jpeg,image/png,image/jpg",limit:1},{default:o(()=>[y.value?(D(),O("img",{key:0,src:y.value,class:"avatar-preview"},null,8,$e)):(D(),he(c,{key:1,class:"avatar-uploader-icon"},{default:o(()=>[a(g(Ne))]),_:1}))]),_:1})]),_:1},8,["modelValue"])])}}},na=re(Je,[["__scopeId","data-v-14ee7135"]]);export{na as default};
