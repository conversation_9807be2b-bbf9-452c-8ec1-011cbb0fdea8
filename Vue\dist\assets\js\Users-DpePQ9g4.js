import{_ as Oe}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                     *//* empty css                   *//* empty css                             *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                *//* empty css               *//* empty css                         */import{r as b,a as H,o as Pe,c as V,b as s,d as e,w as t,aI as He,aJ as Qe,a1 as Ye,f as B,g as _,D as u,i as v,G as M,aC as Ke,aE as Ge,q as c,l as Je,k as We,e as Ze,$ as Xe,aK as et,aL as tt,s as lt,a7 as at,ab as st,ac as ot,Z as nt,_ as dt,B as T,P as it,t as i,aD as rt,aH as ct,a8 as ut,m as S,aM as _t,aN as pt,E as mt,j as ft,ar as Z}from"./index-D5n6Uv5f.js";import{c as X,b as Q}from"./index-DyZSWDSU.js";const vt={class:"users-page"},gt={class:"user-control-panel"},ht={class:"control-header"},yt={class:"page-info"},bt={class:"page-title-section"},kt={class:"page-title"},wt={class:"stats-section"},Ut={class:"stats-cards"},Ct={class:"stat-card primary"},Vt={class:"stat-content"},Dt={class:"stat-number"},xt={class:"stat-icon"},zt={class:"stat-card success"},Et={class:"stat-content"},$t={class:"stat-number"},Ft={class:"stat-icon"},St={class:"quick-actions"},Lt={class:"search-content"},Bt={class:"search-group"},Tt={class:"search-label"},It={class:"search-input-wrapper"},Rt={class:"search-group"},At={class:"search-label"},qt={class:"option-item"},Mt={class:"option-item"},Nt={class:"option-item"},jt={class:"search-actions"},Ot={key:0,class:"quick-filters"},Pt={class:"quick-filters-label"},Ht={class:"quick-filters-tags"},Qt={class:"table-section"},Yt={class:"table-header"},Kt={class:"table-title"},Gt={class:"table-actions"},Jt={class:"row-number"},Wt={class:"user-id"},Zt={class:"user-info"},Xt={class:"user-details"},el={class:"user-name"},tl={class:"user-id-text"},ll={class:"phone-info"},al={class:"phone-number"},sl={key:0},ol={key:1},nl={key:2},dl={class:"location-info"},il={class:"location-main"},rl={class:"location-sub"},cl={class:"date-info"},ul={class:"date-main"},_l={class:"date-time"},pl={class:"action-buttons"},ml={class:"pagination-container"},fl={key:0,class:"user-detail"},vl={class:"user-detail-header"},gl={class:"user-basic-info"},hl={class:"user-nickname"},yl={class:"user-id"},bl={class:"openid-text"},kl={class:"unionid-text"},wl={key:0},Ul={key:1},Cl={key:2},Vl={class:"dialog-footer"},Dl={class:"dialog-footer"},xl={__name:"Users",setup(zl){const I=b(!1),L=b([]),r=b(null),N=b(!1),R=b(!1),Y=b(!1),ee=b(0),te=b(0),le=b(null),g=H({keyword:"",status:""}),p=H({current:1,size:10,total:0}),d=H({id:null,nickname:"",phone:"",gender:0,status:1,city:"",province:"",country:"",avatarUrl:""}),re=H({nickname:[{required:!0,message:"请输入用户昵称",trigger:"blur"},{min:1,max:20,message:"昵称长度在 1 到 20 个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}),D=b([]),y=b([]),ae=(o,l,m="info")=>{D.value.find(A=>A.key===o)||D.value.push({key:o,label:l,type:m})},ce=o=>{const l=D.value.findIndex(m=>m.key===o);l>-1&&(D.value.splice(l,1),o==="keyword"?g.keyword="":o==="status"&&(g.status=""),K())},k=async()=>{I.value=!0;try{const o={current:p.current,size:p.size,keyword:g.keyword,status:g.status},l=await B.getUsers(o);L.value=l.data.records,p.total=l.data.total,ee.value=l.data.total,te.value=l.data.records.filter(m=>m.status===1).length}catch(o){console.error("加载用户列表失败:",o),_.error("加载用户列表失败")}finally{I.value=!1}},K=()=>{if(D.value=[],g.keyword&&ae("keyword",`关键词: ${g.keyword}`,"primary"),g.status!==""){const o=g.status===1?"正常用户":"禁用用户";ae("status",`状态: ${o}`,g.status===1?"success":"danger")}p.current=1,k()},ue=()=>{g.keyword="",g.status="",D.value=[],p.current=1,k(),_.success("搜索条件已重置")},_e=()=>{k(),_({message:"数据已刷新",type:"success",duration:1500,showClose:!1})},pe=o=>{y.value=o,console.log("已选择用户:",o.length,"个")},me=async()=>{if(y.value.length===0){_.warning("请先选择要删除的用户");return}try{await Z.confirm(`确定要删除选中的 ${y.value.length} 个用户吗？此操作不可恢复。`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0});const o=y.value.map(m=>m.id),l=await B.batchDeleteUsers(o);l.code===200?(_.success(l.message||`已删除 ${y.value.length} 个用户`),y.value=[],await k()):_.error(l.message||"删除失败")}catch(o){o!=="cancel"&&(console.error("批量删除用户失败:",o),_.error("删除失败: "+(o.message||"网络错误")))}},G=(o,l)=>{const m=["ID","昵称","电话","性别","城市","状态","注册时间"],n=o.map(f=>[f.id,f.nickName||"未设置昵称",f.phone,f.gender===1?"男":f.gender===2?"女":"未知",f.city||"未设置",f.status===1?"正常":"禁用",new Date(f.createTime).toLocaleString("zh-CN")]),A=[m,...n].map(f=>f.map(O=>`"${O}"`).join(",")).join(`
`),j=new Blob(["\uFEFF"+A],{type:"text/csv;charset=utf-8;"}),x=document.createElement("a"),h=URL.createObjectURL(j);x.setAttribute("href",h),x.setAttribute("download",l),x.style.visibility="hidden",document.body.appendChild(x),x.click(),document.body.removeChild(x)},fe=o=>{switch(o){case"current":if(L.value.length===0){_.warning("当前页面没有用户数据");return}G(L.value,`用户数据_第${p.current}页_${new Date().toLocaleDateString()}.csv`),_.success(`已导出当前页 ${L.value.length} 条用户数据`);break;case"all":_.info("正在导出全部数据，请稍候..."),G(L.value,`全部用户数据_${new Date().toLocaleDateString()}.csv`),_.success("全部用户数据导出完成");break;case"selected":if(y.value.length===0){_.warning("请先选择要导出的用户");return}G(y.value,`选中用户数据_${y.value.length}条_${new Date().toLocaleDateString()}.csv`),_.success(`已导出选中的 ${y.value.length} 条用户数据`);break}},ve=o=>{p.size=o,p.current=1,k()},ge=o=>{p.current=o,k()},he=async o=>{try{try{const l=await B.getUserDetail(o.id);r.value=l.data}catch(l){console.warn("获取用户详情接口失败，使用列表数据:",l),r.value=o}N.value=!0}catch(l){console.error("获取用户详情失败:",l),_.error("获取用户详情失败")}},ye=o=>{d.id=o.id,d.nickname=o.nickname||"",d.phone=o.phone||"",d.gender=o.gender||0,d.status=o.status||1,d.city=o.city||"",d.province=o.province||"",d.country=o.country||"",d.avatarUrl=o.avatarUrl||"",R.value=!0},be=async()=>{try{await le.value.validate(),Y.value=!0,await B.updateUser(d.id,d),_.success("用户信息更新成功"),R.value=!1,k()}catch(o){o!==!1&&(console.error("更新用户信息失败:",o),_.error("更新用户信息失败"))}finally{Y.value=!1}},ke=async o=>{const l=o.status===1?"禁用":"启用",m=o.status===1?0:1;try{await Z.confirm(`确定要${l}用户 "${o.nickname||o.phone}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await B.updateUserStatus(o.id,m),_.success(`${l}成功`),k()}catch(n){n!=="cancel"&&(console.error(`${l}用户失败:`,n),_.error(`${l}用户失败`))}},we=async o=>{try{await Z.confirm(`确定要删除用户 "${o.nickname||o.phone}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await B.deleteUser(o.id),_.success("删除成功"),k()}catch(l){l!=="cancel"&&(console.error("删除用户失败:",l),_.error("删除用户失败"))}};return Pe(()=>{k()}),(o,l)=>{const m=u("User"),n=Je,A=u("UserFilled"),j=u("CircleCheckFilled"),x=u("Refresh"),h=lt,f=Xe,O=u("Search"),Ue=u("QuestionFilled"),$=We,w=Ge,Ce=u("Flag"),se=u("List"),z=tt,Ve=u("CircleCloseFilled"),J=et,De=u("RefreshLeft"),xe=u("Download"),ze=u("ArrowDown"),Ee=u("DocumentCopy"),W=ot,$e=u("FolderOpened"),Fe=u("Select"),Se=st,Le=at,P=Ke,Be=u("Filter"),F=it,oe=He,ne=u("Delete"),U=ct,de=ut,Te=u("Phone"),Ie=u("View"),Re=u("Lock"),Ae=u("Unlock"),qe=rt,Me=Qe,C=pt,Ne=_t,ie=Ye,E=ft,je=mt;return v(),V("div",vt,[s("div",gt,[e(oe,{class:"control-card",shadow:"never"},{header:t(()=>[s("div",ht,[s("div",yt,[s("div",bt,[s("h1",kt,[e(n,{class:"title-icon"},{default:t(()=>[e(m)]),_:1}),l[17]||(l[17]=c(" 用户管理 ",-1))]),l[18]||(l[18]=s("p",{class:"page-subtitle"},"管理系统中的所有用户信息",-1))])]),s("div",wt,[s("div",Ut,[s("div",Ct,[s("div",Vt,[s("div",Dt,i(ee.value),1),l[19]||(l[19]=s("div",{class:"stat-label"},"总用户数",-1))]),s("div",xt,[e(n,null,{default:t(()=>[e(A)]),_:1})])]),s("div",zt,[s("div",Et,[s("div",$t,i(te.value),1),l[20]||(l[20]=s("div",{class:"stat-label"},"活跃用户",-1))]),s("div",Ft,[e(n,null,{default:t(()=>[e(j)]),_:1})])])])]),s("div",St,[e(f,{content:"刷新数据",placement:"bottom"},{default:t(()=>[e(h,{type:"primary",circle:"",onClick:_e,loading:I.value},{default:t(()=>[e(n,null,{default:t(()=>[e(x)]),_:1})]),_:1},8,["loading"])]),_:1})])])]),default:t(()=>[s("div",Lt,[e(P,{gutter:20,align:"middle"},{default:t(()=>[e(w,{span:10},{default:t(()=>[s("div",Bt,[s("label",Tt,[e(n,null,{default:t(()=>[e(m)]),_:1}),l[21]||(l[21]=c(" 关键词搜索 ",-1))]),s("div",It,[e($,{modelValue:g.keyword,"onUpdate:modelValue":l[0]||(l[0]=a=>g.keyword=a),placeholder:"输入用户昵称、手机号或邮箱进行搜索...",clearable:"",size:"large",onKeyup:Ze(K,["enter"]),class:"search-input"},{prefix:t(()=>[e(n,{class:"search-icon"},{default:t(()=>[e(O)]),_:1})]),suffix:t(()=>[e(f,{content:"支持模糊搜索",placement:"top"},{default:t(()=>[e(n,{class:"help-icon"},{default:t(()=>[e(Ue)]),_:1})]),_:1})]),_:1},8,["modelValue"])])])]),_:1}),e(w,{span:6},{default:t(()=>[s("div",Rt,[s("label",At,[e(n,null,{default:t(()=>[e(Ce)]),_:1}),l[22]||(l[22]=c(" 用户状态 ",-1))]),e(J,{modelValue:g.status,"onUpdate:modelValue":l[1]||(l[1]=a=>g.status=a),placeholder:"选择状态",clearable:"",size:"large",class:"status-select"},{default:t(()=>[e(z,{label:"全部状态",value:""},{default:t(()=>[s("div",qt,[e(n,null,{default:t(()=>[e(se)]),_:1}),l[23]||(l[23]=s("span",null,"全部状态",-1))])]),_:1}),e(z,{label:"正常用户",value:1},{default:t(()=>[s("div",Mt,[e(n,{class:"status-icon success"},{default:t(()=>[e(j)]),_:1}),l[24]||(l[24]=s("span",null,"正常用户",-1))])]),_:1}),e(z,{label:"禁用用户",value:0},{default:t(()=>[s("div",Nt,[e(n,{class:"status-icon danger"},{default:t(()=>[e(Ve)]),_:1}),l[25]||(l[25]=s("span",null,"禁用用户",-1))])]),_:1})]),_:1},8,["modelValue"])])]),_:1}),e(w,{span:8},{default:t(()=>[s("div",jt,[e(h,{type:"primary",size:"large",onClick:K,class:"search-btn",loading:I.value},{default:t(()=>[e(n,null,{default:t(()=>[e(O)]),_:1}),l[26]||(l[26]=s("span",null,"搜索用户",-1))]),_:1,__:[26]},8,["loading"]),e(h,{size:"large",onClick:ue,class:"reset-btn"},{default:t(()=>[e(n,null,{default:t(()=>[e(De)]),_:1}),l[27]||(l[27]=s("span",null,"重置条件",-1))]),_:1,__:[27]}),e(Le,{onCommand:fe,class:"export-dropdown"},{dropdown:t(()=>[e(Se,null,{default:t(()=>[e(W,{command:"current"},{default:t(()=>[e(n,null,{default:t(()=>[e(Ee)]),_:1}),l[29]||(l[29]=c(" 导出当前页 ",-1))]),_:1,__:[29]}),e(W,{command:"all"},{default:t(()=>[e(n,null,{default:t(()=>[e($e)]),_:1}),l[30]||(l[30]=c(" 导出全部数据 ",-1))]),_:1,__:[30]}),e(W,{command:"selected",divided:""},{default:t(()=>[e(n,null,{default:t(()=>[e(Fe)]),_:1}),l[31]||(l[31]=c(" 导出已选择 ",-1))]),_:1,__:[31]})]),_:1})]),default:t(()=>[e(h,{size:"large",class:"export-btn"},{default:t(()=>[e(n,null,{default:t(()=>[e(xe)]),_:1}),l[28]||(l[28]=s("span",null,"导出",-1)),e(n,{class:"dropdown-icon"},{default:t(()=>[e(ze)]),_:1})]),_:1,__:[28]})]),_:1})])]),_:1})]),_:1}),D.value.length>0?(v(),V("div",Ot,[s("div",Pt,[e(n,null,{default:t(()=>[e(Be)]),_:1}),l[32]||(l[32]=s("span",null,"快速筛选:",-1))]),s("div",Ht,[(v(!0),V(nt,null,dt(D.value,a=>(v(),T(F,{key:a.key,type:a.type,effect:"light",closable:"",onClose:q=>ce(a.key),class:"quick-filter-tag"},{default:t(()=>[c(i(a.label),1)]),_:2},1032,["type","onClose"]))),128))])])):M("",!0)])]),_:1})]),s("div",Qt,[e(oe,{class:"table-card",shadow:"never"},{header:t(()=>[s("div",Yt,[s("div",Kt,[e(n,null,{default:t(()=>[e(se)]),_:1}),l[33]||(l[33]=c(" 用户列表 ",-1)),y.value.length>0?(v(),T(F,{key:0,type:"primary",size:"small",class:"selected-count"},{default:t(()=>[c(" 已选择 "+i(y.value.length)+" 项 ",1)]),_:1})):M("",!0)]),s("div",Gt,[y.value.length>0?(v(),T(h,{key:0,type:"danger",plain:"",size:"small",onClick:me},{default:t(()=>[e(n,null,{default:t(()=>[e(ne)]),_:1}),l[34]||(l[34]=c(" 批量删除 ",-1))]),_:1,__:[34]})):M("",!0)])])]),default:t(()=>[e(qe,{data:L.value,loading:I.value,class:"modern-table","header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"600",borderBottom:"1px solid #ebeef5"},"row-style":{height:"60px"},"empty-text":"暂无用户数据",onSelectionChange:pe},{default:t(()=>[e(U,{type:"selection",width:"55",align:"center"}),e(U,{label:"序号",width:"70",align:"center"},{default:t(({$index:a})=>[s("span",Jt,i((p.current-1)*p.size+a+1),1)]),_:1}),e(U,{prop:"id",label:"ID",width:"80",align:"center"},{default:t(({row:a})=>[s("span",Wt,"#"+i(a.id),1)]),_:1}),e(U,{label:"用户信息",width:"250"},{default:t(({row:a})=>[s("div",Zt,[e(de,{size:45,src:a.avatarUrl,class:"user-avatar"},{default:t(()=>[e(n,null,{default:t(()=>[e(m)]),_:1})]),_:2},1032,["src"]),s("div",Xt,[s("div",el,i(a.nickname||"未设置昵称"),1),s("div",tl,"ID: "+i(a.id),1)])])]),_:1}),e(U,{prop:"phone",label:"手机号",width:"140",align:"center"},{default:t(({row:a})=>[s("div",ll,[e(n,{class:"phone-icon"},{default:t(()=>[e(Te)]),_:1}),s("span",al,i(a.phone||"未绑定"),1)])]),_:1}),e(U,{prop:"gender",label:"性别",width:"80",align:"center"},{default:t(({row:a})=>[e(F,{type:a.gender===1?"primary":a.gender===2?"danger":"info",effect:"light",size:"small",round:""},{default:t(()=>[a.gender===1?(v(),V("span",sl,"男")):a.gender===2?(v(),V("span",ol,"女")):(v(),V("span",nl,"未知"))]),_:2},1032,["type"])]),_:1}),e(U,{label:"地区信息","min-width":"150"},{default:t(({row:a})=>[s("div",dl,[s("div",il,i(a.city||"未设置"),1),s("div",rl,i(a.province||""),1)])]),_:1}),e(U,{label:"状态",width:"100",align:"center"},{default:t(({row:a})=>[e(F,{type:a.status===1?"success":"danger",effect:"light",round:"",size:"small"},{default:t(()=>[c(i(S(X)(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(U,{prop:"createdAt",label:"注册时间",width:"180",align:"center"},{default:t(({row:a})=>[s("div",cl,[s("div",ul,i(S(Q)(a.createdAt,"YYYY-MM-DD")),1),s("div",_l,i(S(Q)(a.createdAt,"HH:mm:ss")),1)])]),_:1}),e(U,{label:"操作",width:"200",fixed:"right",align:"center"},{default:t(({row:a})=>[s("div",pl,[e(f,{content:"查看详情",placement:"top"},{default:t(()=>[e(h,{type:"primary",plain:"",size:"small",circle:"",onClick:q=>he(a)},{default:t(()=>[e(n,null,{default:t(()=>[e(Ie)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),e(f,{content:a.status===1?"禁用用户":"启用用户",placement:"top"},{default:t(()=>[e(h,{type:a.status===1?"warning":"success",plain:"",size:"small",circle:"",onClick:q=>ke(a)},{default:t(()=>[a.status===1?(v(),T(n,{key:0},{default:t(()=>[e(Re)]),_:1})):(v(),T(n,{key:1},{default:t(()=>[e(Ae)]),_:1}))]),_:2},1032,["type","onClick"])]),_:2},1032,["content"]),e(f,{content:"删除用户",placement:"top"},{default:t(()=>[e(h,{type:"danger",plain:"",size:"small",circle:"",onClick:q=>we(a)},{default:t(()=>[e(n,null,{default:t(()=>[e(ne)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data","loading"])]),_:1})]),s("div",ml,[e(Me,{"current-page":p.current,"onUpdate:currentPage":l[2]||(l[2]=a=>p.current=a),"page-size":p.size,"onUpdate:pageSize":l[3]||(l[3]=a=>p.size=a),"page-sizes":[10,20,50,100],total:p.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:ge},null,8,["current-page","page-size","total"])]),e(ie,{modelValue:N.value,"onUpdate:modelValue":l[6]||(l[6]=a=>N.value=a),title:"用户详情",width:"700px","close-on-click-modal":!1},{footer:t(()=>{var a;return[s("div",Vl,[e(h,{onClick:l[4]||(l[4]=q=>N.value=!1)},{default:t(()=>l[35]||(l[35]=[c("关闭",-1)])),_:1,__:[35]}),((a=r.value)==null?void 0:a.status)===1?(v(),T(h,{key:0,type:"primary",onClick:l[5]||(l[5]=q=>ye(r.value))},{default:t(()=>l[36]||(l[36]=[c(" 编辑用户 ",-1)])),_:1,__:[36]})):M("",!0)])]}),default:t(()=>[r.value?(v(),V("div",fl,[s("div",vl,[e(de,{size:80,src:r.value.avatarUrl,class:"detail-avatar"},{default:t(()=>[e(n,{size:"40"},{default:t(()=>[e(m)]),_:1})]),_:1},8,["src"]),s("div",gl,[s("h3",hl,i(r.value.nickname||"未设置昵称"),1),s("p",yl,"用户ID: #"+i(r.value.id),1),e(F,{type:r.value.status===1?"success":"danger",size:"large",effect:"light"},{default:t(()=>[c(i(S(X)(r.value.status)),1)]),_:1},8,["type"])])]),e(Ne,{column:2,border:"",class:"detail-descriptions"},{default:t(()=>[e(C,{label:"微信OpenID",span:2},{default:t(()=>[s("span",bl,i(r.value.openid||"未绑定"),1)]),_:1}),e(C,{label:"微信UnionID",span:2},{default:t(()=>[s("span",kl,i(r.value.unionid||"未绑定"),1)]),_:1}),e(C,{label:"手机号码"},{default:t(()=>[c(i(r.value.phone||"未绑定"),1)]),_:1}),e(C,{label:"性别"},{default:t(()=>[e(F,{type:r.value.gender===1?"primary":r.value.gender===2?"danger":"info",effect:"light",size:"small"},{default:t(()=>[r.value.gender===1?(v(),V("span",wl,"男")):r.value.gender===2?(v(),V("span",Ul,"女")):(v(),V("span",Cl,"未知"))]),_:1},8,["type"])]),_:1}),e(C,{label:"所在城市"},{default:t(()=>[c(i(r.value.city||"未设置"),1)]),_:1}),e(C,{label:"所在省份"},{default:t(()=>[c(i(r.value.province||"未设置"),1)]),_:1}),e(C,{label:"所在国家"},{default:t(()=>[c(i(r.value.country||"未设置"),1)]),_:1}),e(C,{label:"账户状态"},{default:t(()=>[e(F,{type:r.value.status===1?"success":"danger",effect:"light"},{default:t(()=>[c(i(S(X)(r.value.status)),1)]),_:1},8,["type"])]),_:1}),e(C,{label:"注册时间"},{default:t(()=>[c(i(S(Q)(r.value.createdAt)),1)]),_:1}),e(C,{label:"最后更新"},{default:t(()=>[c(i(S(Q)(r.value.updatedAt)),1)]),_:1})]),_:1})])):M("",!0)]),_:1},8,["modelValue"]),e(ie,{modelValue:R.value,"onUpdate:modelValue":l[16]||(l[16]=a=>R.value=a),title:"编辑用户信息",width:"600px","close-on-click-modal":!1},{footer:t(()=>[s("div",Dl,[e(h,{onClick:l[15]||(l[15]=a=>R.value=!1)},{default:t(()=>l[37]||(l[37]=[c("取消",-1)])),_:1,__:[37]}),e(h,{type:"primary",onClick:be,loading:Y.value},{default:t(()=>l[38]||(l[38]=[c(" 保存 ",-1)])),_:1,__:[38]},8,["loading"])])]),default:t(()=>[e(je,{ref_key:"editFormRef",ref:le,model:d,rules:re,"label-width":"100px",class:"edit-form"},{default:t(()=>[e(P,{gutter:20},{default:t(()=>[e(w,{span:12},{default:t(()=>[e(E,{label:"用户昵称",prop:"nickname"},{default:t(()=>[e($,{modelValue:d.nickname,"onUpdate:modelValue":l[7]||(l[7]=a=>d.nickname=a),placeholder:"请输入用户昵称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(w,{span:12},{default:t(()=>[e(E,{label:"手机号码",prop:"phone"},{default:t(()=>[e($,{modelValue:d.phone,"onUpdate:modelValue":l[8]||(l[8]=a=>d.phone=a),placeholder:"请输入手机号码",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(P,{gutter:20},{default:t(()=>[e(w,{span:12},{default:t(()=>[e(E,{label:"性别",prop:"gender"},{default:t(()=>[e(J,{modelValue:d.gender,"onUpdate:modelValue":l[9]||(l[9]=a=>d.gender=a),placeholder:"请选择性别",style:{width:"100%"}},{default:t(()=>[e(z,{label:"未知",value:0}),e(z,{label:"男",value:1}),e(z,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(w,{span:12},{default:t(()=>[e(E,{label:"账户状态",prop:"status"},{default:t(()=>[e(J,{modelValue:d.status,"onUpdate:modelValue":l[10]||(l[10]=a=>d.status=a),placeholder:"请选择状态",style:{width:"100%"}},{default:t(()=>[e(z,{label:"正常",value:1}),e(z,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(P,{gutter:20},{default:t(()=>[e(w,{span:8},{default:t(()=>[e(E,{label:"所在城市",prop:"city"},{default:t(()=>[e($,{modelValue:d.city,"onUpdate:modelValue":l[11]||(l[11]=a=>d.city=a),placeholder:"请输入城市",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(w,{span:8},{default:t(()=>[e(E,{label:"所在省份",prop:"province"},{default:t(()=>[e($,{modelValue:d.province,"onUpdate:modelValue":l[12]||(l[12]=a=>d.province=a),placeholder:"请输入省份",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(w,{span:8},{default:t(()=>[e(E,{label:"所在国家",prop:"country"},{default:t(()=>[e($,{modelValue:d.country,"onUpdate:modelValue":l[13]||(l[13]=a=>d.country=a),placeholder:"请输入国家",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{label:"头像URL",prop:"avatarUrl"},{default:t(()=>[e($,{modelValue:d.avatarUrl,"onUpdate:modelValue":l[14]||(l[14]=a=>d.avatarUrl=a),placeholder:"请输入头像URL",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}},Hl=Oe(xl,[["__scopeId","data-v-af3d47a8"]]);export{Hl as default};
