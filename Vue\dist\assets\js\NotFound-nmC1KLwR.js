import{_ as l}from"./_plugin-vue_export-helper-BXfX6KIN.js";import{c as u,b as t,d as n,w as s,l as p,s as f,h as m,D as g,i as v,q as d}from"./index-BiIMV38A.js";const k={class:"not-found"},x={class:"not-found-content"},B={class:"not-found-icon"},N={class:"not-found-actions"},C={__name:"NotFound",setup(E){const e=m(),_=()=>{e.push("/")},c=()=>{e.go(-1)};return(V,o)=>{const r=g("Warning"),i=p,a=f;return v(),u("div",k,[t("div",x,[t("div",B,[n(i,{size:120,color:"#909399"},{default:s(()=>[n(r)]),_:1})]),o[2]||(o[2]=t("h1",{class:"not-found-title"},"404",-1)),o[3]||(o[3]=t("p",{class:"not-found-message"},"抱歉，您访问的页面不存在",-1)),t("div",N,[n(a,{type:"primary",onClick:_},{default:s(()=>o[0]||(o[0]=[d(" 返回首页 ",-1)])),_:1,__:[0]}),n(a,{onClick:c},{default:s(()=>o[1]||(o[1]=[d(" 返回上页 ",-1)])),_:1,__:[1]})])])])}}},y=l(C,[["__scopeId","data-v-ada6712d"]]);export{y as default};
