import{_ as re}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                     */import{r as b,a as U,y as ie,o as ue,g as r,c as F,b as n,G as de,d as t,w as s,s as ce,E as me,t as _,I as pe,b8 as _e,B as ge,aJ as fe,a1 as ve,f as y,ar as B,aD as be,i as O,q as c,l as ye,m as h,aO as we,j as he,k as Pe,e as Ve,aK as xe,aL as Ce,N as ke,aT as Ee,b9 as ze,aH as Se,a3 as Ue,aP as Be,aR as Oe,b0 as $e,aF as De,b3 as Te}from"./index-BiIMV38A.js";const Ie={class:"price-categories-page"},Ne={class:"page-header"},Re={class:"search-bar"},Fe={key:0,class:"batch-operations"},je={class:"selection-info"},qe={class:"selected-count"},Ae={class:"selection-stats"},Ke={class:"batch-buttons"},Le={class:"table-container"},Me={class:"category-name"},Ge={class:"name-text"},He={class:"description-text"},Je={class:"price-range"},Qe={class:"price-value"},We={class:"price-value"},Xe={class:"time-text"},Ye={class:"table-info"},Ze={class:"pagination-container"},et={class:"dialog-footer"},tt={__name:"PriceCategories",setup(at){const k=b(!1),V=b([]),u=b([]),g=U({keyword:"",status:null}),i=U({current:1,size:10,total:0}),w=b(!1),P=b(!1),E=b(!1),x=b(),o=U({id:null,name:"",description:"",minPrice:0,maxPrice:0,sortOrder:0,status:1}),j={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:1,max:100,message:"分类名称长度在 1 到 100 个字符",trigger:"blur"}],minPrice:[{required:!0,message:"请输入最低价格",trigger:"blur"},{type:"number",min:0,message:"最低价格不能小于0",trigger:"blur"}],maxPrice:[{required:!0,message:"请输入最高价格",trigger:"blur"},{type:"number",min:0,message:"最高价格不能小于0",trigger:"blur"}],sortOrder:[{type:"number",min:0,message:"排序顺序不能小于0",trigger:"blur"}]},$=ie(()=>{const a=u.value.filter(m=>m.status===1).length,e=u.value.filter(m=>m.status===0).length;return{enabled:a,disabled:e}}),q=a=>a?new Date(a).toLocaleString("zh-CN"):"-",p=async()=>{k.value=!0;try{const a={current:i.current,size:i.size,keyword:g.keyword,status:g.status},e=await y.getPriceCategories(a);e&&e.data?(V.value=e.data.records||[],i.total=e.data.total||0):(V.value=[],i.total=0)}catch(a){console.error("加载价格分类列表失败:",a),r.error("加载价格分类列表失败"),V.value=[],i.total=0}finally{k.value=!1}},D=()=>{i.current=1,p()},A=()=>{g.keyword="",g.status=null,i.current=1,p(),r.success("搜索条件已重置")},K=()=>{p(),r({message:"价格分类数据已刷新",type:"success",duration:1500,showClose:!1})},L=a=>{i.size=a,i.current=1,p()},M=a=>{i.current=a,p()},G=a=>{u.value=a},H=async a=>{try{const e=a.status===1?"启用":"禁用";await y.updatePriceCategoryStatus(a.id,a.status),r.success(`${e}成功`),p()}catch(e){console.error("切换价格分类状态失败:",e),r.error("操作失败"),a.status=a.status===1?0:1}},J=async a=>{try{await B.confirm(`确定要删除价格分类"${a.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await y.deletePriceCategory(a.id),r.success("删除成功"),p()}catch(e){e!=="cancel"&&(console.error("删除价格分类失败:",e),r.error("删除失败"))}},T=async a=>{if(u.value.length===0){r.warning("请先选择要操作的价格分类");return}try{const e=a===1?"启用":"禁用";await B.confirm(`确定要${e}选中的 ${u.value.length} 个价格分类吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const m=u.value.map(d=>y.updatePriceCategoryStatus(d.id,a));await Promise.all(m),r.success(`批量${e}成功`),u.value=[],p()}catch(e){e!=="cancel"&&(console.error("批量操作失败:",e),r.error("批量操作失败"))}},Q=async()=>{if(u.value.length===0){r.warning("请先选择要删除的价格分类");return}try{await B.confirm(`确定要删除选中的 ${u.value.length} 个价格分类吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=u.value.map(e=>y.deletePriceCategory(e.id));await Promise.all(a),r.success("批量删除成功"),u.value=[],p()}catch(a){a!=="cancel"&&(console.error("批量删除失败:",a),r.error("批量删除失败"))}},W=()=>{P.value=!1,Y(),w.value=!0},X=a=>{P.value=!0,Object.assign(o,{id:a.id,name:a.name,description:a.description,minPrice:a.minPrice,maxPrice:a.maxPrice,sortOrder:a.sortOrder,status:a.status}),w.value=!0},Y=()=>{Object.assign(o,{id:null,name:"",description:"",minPrice:0,maxPrice:0,sortOrder:0,status:1}),x.value&&x.value.resetFields()},Z=async()=>{try{if(await x.value.validate(),o.maxPrice<=o.minPrice){r.error("最高价格必须大于最低价格");return}E.value=!0,P.value?(await y.updatePriceCategory(o.id,o),r.success("价格分类更新成功！")):(await y.createPriceCategory(o),r.success("价格分类创建成功！")),w.value=!1,await p()}catch(a){console.error("提交失败:",a),r.error("提交失败: "+a.message)}finally{E.value=!1}};return ue(async()=>{console.log("价格分类管理页面已挂载，开始加载数据...");try{await p(),console.log("价格分类数据加载完成")}catch(a){console.error("价格分类数据加载失败:",a),r.error("价格分类数据加载失败: "+a.message)}}),(a,e)=>{const m=ye,d=ce,z=Pe,f=he,I=Ce,ee=xe,N=me,v=Se,te=Ue,ae=be,le=fe,S=$e,R=Te,se=De,oe=ve,ne=_e;return O(),F("div",Ie,[n("div",Ne,[e[15]||(e[15]=n("h1",{class:"page-title"},"价格分类管理",-1)),t(d,{type:"primary",onClick:W},{default:s(()=>[t(m,null,{default:s(()=>[t(h(we))]),_:1}),e[14]||(e[14]=c(" 新增价格分类 ",-1))]),_:1,__:[14]})]),n("div",Re,[t(N,{model:g,inline:""},{default:s(()=>[t(f,{label:"关键词"},{default:s(()=>[t(z,{modelValue:g.keyword,"onUpdate:modelValue":e[0]||(e[0]=l=>g.keyword=l),placeholder:"请输入分类名称或描述",clearable:"",style:{width:"200px"},onKeyup:Ve(D,["enter"])},null,8,["modelValue"])]),_:1}),t(f,{label:"状态"},{default:s(()=>[t(ee,{modelValue:g.status,"onUpdate:modelValue":e[1]||(e[1]=l=>g.status=l),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:s(()=>[t(I,{label:"启用",value:1}),t(I,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),t(f,null,{default:s(()=>[t(d,{type:"primary",onClick:D},{default:s(()=>[t(m,null,{default:s(()=>[t(h(ke))]),_:1}),e[16]||(e[16]=c(" 搜索 ",-1))]),_:1,__:[16]}),t(d,{onClick:A},{default:s(()=>[t(m,null,{default:s(()=>[t(h(Ee))]),_:1}),e[17]||(e[17]=c(" 重置 ",-1))]),_:1,__:[17]}),t(d,{onClick:K},{default:s(()=>[t(m,null,{default:s(()=>[t(h(ze))]),_:1}),e[18]||(e[18]=c(" 刷新 ",-1))]),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),u.value.length>0?(O(),F("div",Fe,[n("div",je,[n("span",qe,"已选择 "+_(u.value.length)+" 项",1),n("span",Ae,_($.value.enabled)+" 个启用，"+_($.value.disabled)+" 个禁用 ",1)]),n("div",Ke,[t(d,{type:"success",size:"small",onClick:e[2]||(e[2]=l=>T(1))},{default:s(()=>e[19]||(e[19]=[c(" 批量启用 ",-1)])),_:1,__:[19]}),t(d,{type:"warning",size:"small",onClick:e[3]||(e[3]=l=>T(0))},{default:s(()=>e[20]||(e[20]=[c(" 批量禁用 ",-1)])),_:1,__:[20]}),t(d,{type:"danger",size:"small",onClick:Q},{default:s(()=>e[21]||(e[21]=[c(" 批量删除 ",-1)])),_:1,__:[21]})])])):de("",!0),n("div",Le,[pe((O(),ge(ae,{data:V.value,stripe:"",border:"",style:{width:"100%"},onSelectionChange:G},{default:s(()=>[t(v,{type:"selection",width:"55",align:"center"}),t(v,{prop:"id",label:"ID",width:"80",align:"center"}),t(v,{prop:"name",label:"分类名称",width:"150",align:"center"},{default:s(({row:l})=>[n("div",Me,[n("div",Ge,_(l.name),1)])]),_:1}),t(v,{prop:"description",label:"描述","min-width":"200",align:"center"},{default:s(({row:l})=>[n("div",He,_(l.description||"-"),1)]),_:1}),t(v,{label:"价格范围",width:"180",align:"center"},{default:s(({row:l})=>[n("div",Je,[n("span",Qe,"¥"+_(l.minPrice),1),e[22]||(e[22]=n("span",{class:"separator"}," - ",-1)),n("span",We,"¥"+_(l.maxPrice===99999?"∞":l.maxPrice),1)])]),_:1}),t(v,{prop:"sortOrder",label:"排序",width:"100",align:"center"}),t(v,{prop:"status",label:"状态",width:"80",align:"center"},{default:s(({row:l})=>[t(te,{modelValue:l.status,"onUpdate:modelValue":C=>l.status=C,"active-value":1,"inactive-value":0,onChange:C=>H(l)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(v,{prop:"createdAt",label:"创建时间",width:"180",align:"center"},{default:s(({row:l})=>[n("div",Xe,_(q(l.createdAt)),1)]),_:1}),t(v,{label:"操作",width:"200",align:"center",fixed:"right"},{default:s(({row:l})=>[t(d,{type:"primary",size:"small",onClick:C=>X(l)},{default:s(()=>[t(m,null,{default:s(()=>[t(h(Be))]),_:1}),e[23]||(e[23]=c(" 编辑 ",-1))]),_:2,__:[23]},1032,["onClick"]),t(d,{type:"danger",size:"small",onClick:C=>J(l)},{default:s(()=>[t(m,null,{default:s(()=>[t(h(Oe))]),_:1}),e[24]||(e[24]=c(" 删除 ",-1))]),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ne,k.value]]),n("div",Ye,[n("span",null,"共 "+_(i.total)+" 条记录",1)])]),n("div",Ze,[t(le,{"current-page":i.current,"onUpdate:currentPage":e[4]||(e[4]=l=>i.current=l),"page-size":i.size,"onUpdate:pageSize":e[5]||(e[5]=l=>i.size=l),"page-sizes":[10,20,50,100],total:i.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:L,onCurrentChange:M},null,8,["current-page","page-size","total"])]),t(oe,{modelValue:w.value,"onUpdate:modelValue":e[13]||(e[13]=l=>w.value=l),title:P.value?"编辑价格分类":"新增价格分类",width:"600px","close-on-click-modal":!1},{footer:s(()=>[n("div",et,[t(d,{onClick:e[12]||(e[12]=l=>w.value=!1)},{default:s(()=>e[27]||(e[27]=[c("取消",-1)])),_:1,__:[27]}),t(d,{type:"primary",onClick:Z,loading:E.value},{default:s(()=>[c(_(P.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:s(()=>[t(N,{ref_key:"formRef",ref:x,model:o,rules:j,"label-width":"100px"},{default:s(()=>[t(f,{label:"分类名称",prop:"name"},{default:s(()=>[t(z,{modelValue:o.name,"onUpdate:modelValue":e[6]||(e[6]=l=>o.name=l),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),t(f,{label:"描述",prop:"description"},{default:s(()=>[t(z,{modelValue:o.description,"onUpdate:modelValue":e[7]||(e[7]=l=>o.description=l),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])]),_:1}),t(f,{label:"最低价格",prop:"minPrice"},{default:s(()=>[t(S,{modelValue:o.minPrice,"onUpdate:modelValue":e[8]||(e[8]=l=>o.minPrice=l),min:0,precision:2,placeholder:"请输入最低价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(f,{label:"最高价格",prop:"maxPrice"},{default:s(()=>[t(S,{modelValue:o.maxPrice,"onUpdate:modelValue":e[9]||(e[9]=l=>o.maxPrice=l),min:0,precision:2,placeholder:"请输入最高价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(f,{label:"排序顺序",prop:"sortOrder"},{default:s(()=>[t(S,{modelValue:o.sortOrder,"onUpdate:modelValue":e[10]||(e[10]=l=>o.sortOrder=l),min:0,placeholder:"请输入排序顺序",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(f,{label:"状态",prop:"status"},{default:s(()=>[t(se,{modelValue:o.status,"onUpdate:modelValue":e[11]||(e[11]=l=>o.status=l)},{default:s(()=>[t(R,{label:1},{default:s(()=>e[25]||(e[25]=[c("启用",-1)])),_:1,__:[25]}),t(R,{label:0},{default:s(()=>e[26]||(e[26]=[c("禁用",-1)])),_:1,__:[26]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},bt=re(tt,[["__scopeId","data-v-e3e14651"]]);export{bt as default};
