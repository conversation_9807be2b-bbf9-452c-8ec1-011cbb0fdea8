#!/bin/bash

# 花店后端项目部署脚本
# 服务器地址: www.mxm.qiangs.xyz

echo "=== 花店后端项目部署脚本 ==="

# 设置变量
APP_NAME="flower-shop"
JAR_NAME="flower-shop-1.0.0.jar"
APP_PORT=8080
PROFILE="prod"

# 检查Java环境
echo "检查Java环境..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java环境，请先安装Java 17"
    exit 1
fi

# 停止现有进程
echo "停止现有进程..."
PID=$(ps -ef | grep $JAR_NAME | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "发现运行中的进程 PID: $PID，正在停止..."
    kill -9 $PID
    sleep 3
fi

# 创建日志目录
echo "创建日志目录..."
mkdir -p logs

# 备份旧版本
if [ -f "$JAR_NAME" ]; then
    echo "备份旧版本..."
    mv $JAR_NAME ${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)
fi

# 启动应用
echo "启动应用..."
nohup java -jar $JAR_NAME --spring.profiles.active=$PROFILE > logs/startup.log 2>&1 &

# 等待启动
echo "等待应用启动..."
sleep 10

# 检查启动状态
PID=$(ps -ef | grep $JAR_NAME | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "应用启动成功！"
    echo "进程ID: $PID"
    echo "端口: $APP_PORT"
    echo "访问地址: http://www.mxm.qiangs.xyz:$APP_PORT/api"
    echo "日志文件: logs/flower-shop.log"
else
    echo "应用启动失败，请检查日志文件 logs/startup.log"
    exit 1
fi

echo "=== 部署完成 ==="
