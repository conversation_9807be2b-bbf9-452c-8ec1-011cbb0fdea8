import{_ as Pe}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                    *//* empty css                        *//* empty css               *//* empty css                         */import{u as El,r as x,y as De,A as Pl,c as _,i as u,G as N,d as l,B as S,w as a,b as r,l as Be,m as g,aO as _e,aP as Re,aQ as Dl,q as p,aR as ye,s as Te,t as f,aS as Bl,g as d,ar as ee,a as ve,o as Rl,aC as Tl,aD as Ol,aJ as Ml,a1 as Ll,f as O,D as te,aE as Al,k as Ql,e as jl,N as ke,aK as Nl,Z as A,_ as Q,aL as Jl,aT as ql,a7 as Kl,aU as Wl,aa as Gl,ab as Hl,ac as Zl,aV as Xl,ak as Ve,aW as Yl,P as ea,aX as Ie,aY as xe,aH as la,aZ as aa,ap as ta,a_ as se,a$ as sa,p as oa,a4 as ue,$ as na,E as ra,j as ia,b0 as ua,b1 as $e,b2 as ze,aF as da,b3 as ca}from"./index-DylWFde9.js";import{f as de,d as Se,b as Fe,e as pa}from"./index-Bpa9ymHh.js";/* empty css                  */const fa={class:"image-upload"},ma={key:0,class:"main-image-upload"},ga={key:0,class:"upload-placeholder"},va={key:1,class:"uploaded-image"},_a=["src"],ya={class:"image-overlay"},ba={key:1,class:"detail-images-upload"},ha={class:"upload-btn"},wa={class:"upload-tip"},Ca={key:2,class:"upload-progress"},ka={class:"progress-text"},Ue="/api/admin/upload/image",Va={__name:"ImageUpload",props:{type:{type:String,default:"main",validator:J=>["main","detail"].includes(J)},modelValue:{type:[String,Array],default:()=>[]},maxCount:{type:Number,default:10},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(J,{emit:Z}){const k=J,U=Z,w=El(),E=x(!1),B=x(!1),P=x(0),D=x(""),h=x([]);k.type==="main"?D.value=k.modelValue||"":Array.isArray(k.modelValue)&&(h.value=k.modelValue.map((i,m)=>({uid:Date.now()+m,name:`image_${m}`,status:"success",url:i})));const K=De(()=>({Authorization:`Bearer ${w.token}`}));Pl(()=>k.modelValue,i=>{k.type==="main"?D.value=i||"":Array.isArray(i)?h.value=i.map((m,y)=>({uid:Date.now()+y,name:`image_${y}`,status:"success",url:m})):h.value=[]},{immediate:!0});const W=i=>{if(!i.type.startsWith("image/"))return d.error("只能上传图片文件!"),!1;if(!(i.size/1024/1024<5))return d.error("图片大小不能超过 5MB!"),!1;if(k.type==="detail"&&h.value.length>=k.maxCount)return d.error(`最多只能上传 ${k.maxCount} 张图片!`),!1;E.value=!0,P.value=0;const F=setInterval(()=>{P.value<90?P.value+=Math.random()*30:clearInterval(F)},200);return!0},M=i=>{E.value=!1,P.value=100,i.code===200?(D.value=i.data.url,U("update:modelValue",i.data.url),U("change",i.data.url),d.success("主图上传成功!")):d.error(i.message||"上传失败"),setTimeout(()=>{P.value=0},1e3)},L=(i,m)=>{if(E.value=!1,P.value=100,i.code===200){const y=h.value.findIndex(G=>G.uid===m.uid);y!==-1?(h.value[y].url=i.data.url,h.value[y].status="success"):h.value.push({uid:m.uid,name:m.name,status:"success",url:i.data.url});const F=h.value.map(G=>G.url).filter(Boolean);U("update:modelValue",F),U("change",F),d.success("图片上传成功!")}else{const y=h.value.findIndex(F=>F.uid===m.uid);y!==-1&&h.value.splice(y,1),d.error(i.message||"上传失败")}setTimeout(()=>{P.value=0},1e3)},n=i=>{E.value=!1,P.value=0,console.error("上传失败:",i),d.error("上传失败，请重试")},ce=i=>{i.url&&window.open(i.url,"_blank")},$=i=>{const m=h.value.findIndex(F=>F.uid===i.uid);m!==-1&&h.value.splice(m,1);const y=h.value.map(F=>F.url).filter(Boolean);U("update:modelValue",y),U("change",y),d.success("图片已删除")},b=async()=>{try{await ee.confirm("确定要删除主图吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),B.value=!0,D.value="",U("update:modelValue",""),U("change",""),d.success("主图删除成功!")}catch(i){i!=="cancel"&&d.error("删除失败")}finally{B.value=!1}};return(i,m)=>{const y=Be,F=Dl,G=Te,R=Bl;return u(),_("div",fa,[J.type==="main"?(u(),_("div",ma,[l(F,{class:"main-upload",action:Ue,headers:K.value,"show-file-list":!1,"on-success":M,"on-error":n,"before-upload":W,disabled:E.value,accept:"image/*",drag:""},{default:a(()=>[D.value?(u(),_("div",va,[r("img",{src:D.value,alt:"主图"},null,8,_a),r("div",ya,[l(y,{class:"overlay-icon"},{default:a(()=>[l(g(Re))]),_:1}),m[2]||(m[2]=r("span",null,"点击更换",-1))])])):(u(),_("div",ga,[l(y,{class:"upload-icon"},{default:a(()=>[l(g(_e))]),_:1}),m[0]||(m[0]=r("div",{class:"upload-text"},"点击或拖拽上传主图",-1)),m[1]||(m[1]=r("div",{class:"upload-hint"},"支持 jpg、png、gif 格式，大小不超过5MB",-1))]))]),_:1},8,["headers","disabled"]),D.value?(u(),S(G,{key:0,type:"danger",size:"small",class:"delete-main-btn",onClick:b,loading:B.value},{default:a(()=>[l(y,null,{default:a(()=>[l(g(ye))]),_:1}),m[3]||(m[3]=p(" 删除主图 ",-1))]),_:1,__:[3]},8,["loading"])):N("",!0)])):J.type==="detail"?(u(),_("div",ba,[l(F,{class:"detail-upload",action:Ue,headers:K.value,"file-list":h.value,"on-success":L,"on-error":n,"on-remove":$,"before-upload":W,"on-preview":ce,disabled:E.value,accept:"image/*","list-type":"picture-card",multiple:"",limit:J.maxCount,"auto-upload":!0},{tip:a(()=>[r("div",wa," 最多上传"+f(J.maxCount)+"张图片，支持 jpg、png、gif 格式，单张大小不超过5MB ",1)]),default:a(()=>[r("div",ha,[l(y,null,{default:a(()=>[l(g(_e))]),_:1}),m[4]||(m[4]=r("div",{class:"upload-text"},"添加图片",-1))])]),_:1},8,["headers","file-list","disabled","limit"])])):N("",!0),E.value?(u(),_("div",Ca,[l(R,{percentage:P.value,"show-text":!1},null,8,["percentage"]),r("span",ka,"上传中... "+f(P.value)+"%",1)])):N("",!0)])}}},Ee=Pe(Va,[["__scopeId","data-v-5e5a2665"]]),Ia={class:"page-container"},xa={class:"page-header"},$a={class:"search-bar mb-16"},za={class:"table-info"},Sa={key:0,class:"batch-actions"},Fa={class:"batch-info"},Ua={class:"batch-summary"},Ea={class:"summary-item"},Pa={class:"summary-value"},Da={class:"summary-item"},Ba={class:"summary-value"},Ra={class:"summary-item"},Ta={class:"summary-value"},Oa={class:"summary-item"},Ma={class:"summary-value"},La={class:"summary-item"},Aa={class:"summary-value"},Qa={class:"summary-item"},ja={class:"summary-value"},Na={class:"batch-buttons"},Ja={class:"table-container"},qa={class:"row-number"},Ka={class:"product-image"},Wa={class:"image-error"},Ga={class:"product-name"},Ha={class:"name-text"},Za={class:"description-text"},Xa={class:"filter-header"},Ya={class:"filter-dropdown"},et={class:"filter-actions"},lt={class:"filter-header"},at={class:"filter-dropdown"},tt={class:"filter-actions"},st={class:"price-display"},ot={class:"current-price"},nt={key:0,class:"original-price"},rt={class:"filter-header"},it={class:"filter-dropdown"},ut={class:"filter-actions"},dt={class:"tags-cell"},ct={key:0,class:"no-data"},pt={class:"filter-header"},ft={class:"filter-dropdown"},mt={class:"color-option"},gt={class:"filter-actions"},vt={key:0,class:"color-display"},_t={class:"color-text"},yt={key:1,class:"no-data"},bt={key:1,class:"no-data"},ht={class:"flower-language-cell"},wt={key:0,class:"flower-language-text"},Ct={key:1,class:"no-data"},kt={class:"occasion-cell"},Vt={key:0,class:"occasion-text"},It={key:1,class:"no-data"},xt={class:"care-instructions-cell"},$t={class:"care-instructions-preview"},zt={key:1,class:"no-data"},St={class:"filter-header"},Ft={class:"filter-dropdown"},Ut={class:"filter-actions"},Et={class:"time-text"},Pt={class:"action-buttons"},Dt={class:"pagination-container"},Bt={class:"color-input-wrapper"},Rt={class:"color-option"},Tt={class:"color-name"},Ot=["title"],Mt={class:"size-option"},Lt={class:"size-name"},At={class:"dialog-footer"},Qt={__name:"Flowers",setup(J){const Z=x(!1),k=x([]),U=x([]),w=x([]),E=x(!1),B=x(!1),P=x(!1),D=x(),h=x([]),K=["红色","粉色","白色","黄色","紫色","蓝色","橙色","绿色"],W=["小花束","中等花束","大花束","单支","花盒","花篮"],M=x([...K]),L=x([...W]),n=ve({id:null,name:"",categoryId:null,price:0,originalPrice:null,stockQuantity:0,salesCount:0,description:"",mainImage:"",images:"",tags:"",flowerLanguage:"",careInstructions:"",occasion:"",color:"",size:"",isFeatured:0,status:1}),ce={name:[{required:!0,message:"请输入商品名称",trigger:"blur"},{min:1,max:100,message:"商品名称长度在 1 到 100 个字符",trigger:"blur"}],categoryId:[{required:!0,message:"请选择商品分类",trigger:"change"}],price:[{required:!0,message:"请输入商品价格",trigger:"blur"},{type:"number",min:.01,message:"商品价格必须大于0",trigger:"blur"}],stockQuantity:[{required:!0,message:"请输入库存数量",trigger:"blur"},{type:"number",min:0,message:"库存数量不能小于0",trigger:"blur"}]},$=ve({keyword:"",categoryId:null,status:null}),b=ve({current:1,size:10,total:0}),i=x({category:[],status:[],isFeatured:[],color:[],size:[],priceRange:[],stockRange:[]}),m=x({categories:[],statuses:[{label:"上架",value:1},{label:"下架",value:0}],featuredOptions:[{label:"精选",value:1},{label:"普通",value:0}],colors:[],sizes:[],priceRanges:[],stockRanges:[{label:"缺货(0)",value:"0-0"},{label:"极少库存(1-5)",value:"1-5"},{label:"库存不足(6-10)",value:"6-10"},{label:"库存偏少(11-20)",value:"11-20"},{label:"库存适中(21-50)",value:"21-50"},{label:"库存充足(51-100)",value:"51-100"},{label:"库存丰富(101-200)",value:"101-200"},{label:"库存充裕(201-500)",value:"201-500"},{label:"库存超多(500+)",value:"500-9999"}]}),y=De(()=>{if(w.value.length===0)return{totalAmount:"0.00",totalStock:0,categoryCount:0,onlineCount:0,offlineCount:0};const s=w.value.reduce((V,j)=>V+(parseFloat(j.price)||0),0),e=w.value.reduce((V,j)=>V+(parseInt(j.stockQuantity)||0),0),c=new Set(w.value.map(V=>V.categoryId)).size,v=w.value.filter(V=>V.status===1).length,C=w.value.filter(V=>V.status===0).length;return{totalAmount:s.toFixed(2),totalStock:e,categoryCount:c,onlineCount:v,offlineCount:C}}),F=async()=>{try{const s=await O.getCategories();U.value=s.data.records||s.data,m.value.categories=U.value.map(e=>({label:e.name,value:e.id}))}catch(s){console.error("加载分类列表失败:",s)}},G=async()=>{try{const s=await O.getActivePriceCategories();s&&s.data&&(m.value.priceRanges=s.data.map(e=>{const o=parseFloat(e.minPrice),c=parseFloat(e.maxPrice),v=c>=99999?"∞":`¥${c}`;return{label:`${e.name}(¥${o}-${v})`,value:`${e.minPrice}-${e.maxPrice}`}}))}catch(s){console.error("加载价格分类失败:",s),d.error("加载价格分类失败")}},R=()=>{console.log("应用筛选:",i.value),b.current=1,T()},Oe=()=>{i.value.category=[],R()},Me=()=>{i.value.status=[],R()},Le=()=>{i.value.priceRange=[],R()},Ae=()=>{i.value.stockRange=[],R()},Qe=()=>{i.value.color=[],R()},je=s=>{console.log("分类筛选:",s)},Ne=s=>{console.log("状态筛选:",s)},Je=s=>{console.log("价格筛选:",s)},qe=s=>{console.log("库存筛选:",s)},Ke=s=>{console.log("颜色筛选:",s)},T=async()=>{Z.value=!0;try{const s={current:b.current,size:b.size,keyword:$.keyword,categoryId:$.categoryId,status:$.status};i.value.category.length>0&&(s.categories=i.value.category.join(",")),i.value.status.length>0&&(s.statuses=i.value.status.join(",")),i.value.color.length>0&&(s.colors=i.value.color.join(",")),i.value.size.length>0&&(s.sizes=i.value.size.join(",")),i.value.priceRange.length>0&&(s.priceRanges=i.value.priceRange.join(",")),i.value.stockRange.length>0&&(s.stockRanges=i.value.stockRange.join(",")),Object.keys(s).forEach(o=>{(s[o]===null||s[o]===void 0||s[o]==="")&&delete s[o]}),console.log("请求参数:",s);const e=await O.getFlowers(s);e&&e.data?(k.value=e.data.records||[],b.total=e.data.total||0,console.log("商品数据:",{records:k.value.length,total:b.total,current:b.current,size:b.size})):(k.value=[],b.total=0)}catch(s){console.error("加载商品列表失败:",s),d.error("加载商品列表失败"),k.value=[],b.total=0}finally{Z.value=!1}},be=()=>{b.current=1,T()},We=()=>{$.keyword="",$.categoryId=null,$.status=null,i.value={category:[],status:[],isFeatured:[],color:[],size:[],priceRange:[],stockRange:[]},b.current=1,T(),d.success("搜索条件和筛选条件已重置")},Ge=()=>{T(),d({message:"商品数据已刷新",type:"success",duration:1500,showClose:!1})},He=s=>s?s.split(",").filter(e=>e.trim()).map(e=>e.trim()):[],oe=s=>{if(!s)return"#909399";if(s.startsWith("#")||s.startsWith("rgb"))return s;const e={红色:"#e74c3c",粉色:"#ff6b9d",白色:"#ffffff",黄色:"#f1c40f",紫色:"#9b59b6",蓝色:"#3498db",橙色:"#e67e22",绿色:"#27ae60",黑色:"#2c3e50",灰色:"#95a5a6",棕色:"#8b4513",金色:"#ffd700",银色:"#bdc3c7",玫红:"#e91e63",天蓝:"#87ceeb",薄荷绿:"#98fb98",淡紫:"#dda0dd",珊瑚:"#ff7f50",深红:"#8b0000",深蓝:"#00008b",深绿:"#006400",浅蓝:"#add8e6",浅绿:"#90ee90",浅粉:"#ffb6c1",深紫:"#4b0082",橘红:"#ff4500",青色:"#00ffff",洋红:"#ff00ff",柠檬黄:"#fffacd",米色:"#f5f5dc",卡其色:"#f0e68c",橄榄色:"#808000",海军蓝:"#000080",栗色:"#800000",茶色:"#d2691e",巧克力色:"#d2691e",土黄:"#daa520",森林绿:"#228b22",石灰绿:"#32cd32",春绿:"#00ff7f",青绿:"#008b8b",钢蓝:"#4682b4",皇家蓝:"#4169e1",中紫:"#9370db",深洋红:"#8b008b",深粉:"#ff1493",热粉:"#ff69b4",番茄红:"#ff6347",橙红:"#ff4500",暗橙:"#ff8c00"};if(e[s])return e[s];const o=s.toLowerCase(),c={red:"#ff0000",pink:"#ffc0cb",white:"#ffffff",yellow:"#ffff00",purple:"#800080",blue:"#0000ff",orange:"#ffa500",green:"#008000",black:"#000000",gray:"#808080",grey:"#808080",brown:"#a52a2a",gold:"#ffd700",silver:"#c0c0c0",cyan:"#00ffff",magenta:"#ff00ff",lime:"#00ff00",navy:"#000080",maroon:"#800000",olive:"#808000",teal:"#008080",aqua:"#00ffff",fuchsia:"#ff00ff",coral:"#ff7f50",salmon:"#fa8072",khaki:"#f0e68c",violet:"#ee82ee",indigo:"#4b0082",turquoise:"#40e0d0",crimson:"#dc143c",chocolate:"#d2691e",tan:"#d2b48c",beige:"#f5f5dc",ivory:"#fffff0",lavender:"#e6e6fa",plum:"#dda0dd",orchid:"#da70d6"};for(const[C,V]of Object.entries(c))if(o.includes(C))return V;const v={红:"#ff0000",粉:"#ffc0cb",白:"#ffffff",黄:"#ffff00",紫:"#800080",蓝:"#0000ff",橙:"#ffa500",绿:"#008000",黑:"#000000",灰:"#808080",棕:"#a52a2a",金:"#ffd700",银:"#c0c0c0",青:"#00ffff",洋红:"#ff00ff"};for(const[C,V]of Object.entries(v))if(s.includes(C))return V;return Ze(s)},Ze=s=>{let e=0;for(let C=0;C<s.length;C++)e=s.charCodeAt(C)+((e<<5)-e);const o=Math.abs(e)%360,c=60+Math.abs(e)%40,v=45+Math.abs(e)%20;return`hsl(${o}, ${c}%, ${v}%)`},Xe=(s,e)=>s?s.length<=e?s:s.substring(0,e)+"...":"",Ye=s=>{s&&!M.value.includes(s)&&(M.value.push(s),localStorage.setItem("flowerColors",JSON.stringify(M.value)))},el=s=>{s&&!L.value.includes(s)&&(L.value.push(s),localStorage.setItem("flowerSizes",JSON.stringify(L.value)))},pe=s=>!K.includes(s),fe=s=>!W.includes(s),ll=s=>{if(pe(s)){const e=M.value.indexOf(s);if(e>-1){M.value.splice(e,1);const o=M.value.filter(c=>pe(c));localStorage.setItem("flowerColors",JSON.stringify([...K,...o])),d.success(`已删除颜色选项：${s}`)}}},al=s=>{if(fe(s)){const e=L.value.indexOf(s);if(e>-1){L.value.splice(e,1);const o=L.value.filter(c=>fe(c));localStorage.setItem("flowerSizes",JSON.stringify([...W,...o])),d.success(`已删除规格选项：${s}`)}}},tl=()=>{try{const s=localStorage.getItem("flowerColors");if(s){const o=JSON.parse(s);M.value=[...new Set([...K,...o])]}const e=localStorage.getItem("flowerSizes");if(e){const o=JSON.parse(e);L.value=[...new Set([...W,...o])]}}catch(s){console.error("加载存储选项失败:",s)}},sl=s=>{b.size=s,b.current=1,T()},ol=s=>{b.current=s,T()},nl=async s=>{try{const e=s.status===1?"下架":"上架";await ee.confirm(`确定要${e}商品"${s.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await O.updateFlowerStatus(s.id,s.status===1?0:1),d.success(`${e}成功`),T()}catch(e){e!=="cancel"&&(console.error("切换商品状态失败:",e),d.error("操作失败"))}},rl=async s=>{try{await ee.confirm(`确定要删除商品"${s.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await O.deleteFlower(s.id),d.success("删除成功"),T()}catch(e){e!=="cancel"&&(console.error("删除商品失败:",e),d.error("删除失败"))}},il=s=>{w.value=s},he=async s=>{if(w.value.length===0){d.warning("请先选择要操作的商品");return}const e=w.value.filter(o=>o.status!==s);if(e.length===0){const o=s===1?"上架":"下架";d.warning(`选中的商品都已经是${o}状态`);return}try{const o=s===1?"上架":"下架";await ee.confirm(`确定要${o}选中的 ${e.length} 个商品吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const c=e.map(v=>O.updateFlowerStatus(v.id,s));await Promise.all(c),d.success(`批量${o}成功，共操作 ${e.length} 个商品`),w.value=[],T()}catch(o){o!=="cancel"&&(console.error("批量操作失败:",o),d.error("批量操作失败"))}},ul=async()=>{if(w.value.length===0){d.warning("请先选择要删除的商品");return}try{await ee.confirm(`确定要删除选中的 ${w.value.length} 个商品吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=w.value.map(e=>O.deleteFlower(e.id));await Promise.all(s),d.success("批量删除成功"),w.value=[],T()}catch(s){s!=="cancel"&&(console.error("批量删除失败:",s),d.error("批量删除失败"))}},dl=()=>{B.value=!1,we(),E.value=!0},cl=async s=>{B.value=!0,we();try{const o=(await O.getFlowerDetail(s.id)).data;if(n.id=o.id,n.name=o.name,n.categoryId=o.categoryId,n.price=o.price,n.originalPrice=o.originalPrice,n.stockQuantity=o.stockQuantity,n.salesCount=o.salesCount||0,n.description=o.description||"",n.mainImage=o.mainImage||"",n.images=o.images||"",n.tags=o.tags||"",n.flowerLanguage=o.flowerLanguage||"",n.careInstructions=o.careInstructions||"",n.occasion=o.occasion||"",n.color=o.color||"",n.size=o.size||"",n.isFeatured=o.isFeatured!==void 0?o.isFeatured:0,n.status=o.status!==void 0?o.status:1,o.images)try{h.value=JSON.parse(o.images)}catch{h.value=[]}else h.value=[];E.value=!0}catch(e){console.error("加载商品详情失败:",e),d.error("加载商品详情失败")}},we=()=>{n.id=null,n.name="",n.categoryId=null,n.price=0,n.originalPrice=null,n.stockQuantity=0,n.salesCount=0,n.description="",n.mainImage="",n.images="",n.tags="",n.flowerLanguage="",n.careInstructions="",n.occasion="",n.color="",n.size="",n.isFeatured=0,n.status=1,h.value=[],D.value&&D.value.clearValidate()},pl=s=>{n.mainImage=s},fl=s=>{h.value=s},ml=async()=>{if(D.value)try{await D.value.validate(),await gl()}catch(s){console.error("表单验证失败:",s)}},gl=async()=>{try{const s=vl();await ee.confirm(s.html,B.value?"确认修改商品信息":"确认创建商品",{confirmButtonText:B.value?"确认修改":"确认创建",cancelButtonText:"返回修改",type:"warning",dangerouslyUseHTMLString:!0,customClass:"data-confirm-dialog",beforeClose:(e,o,c)=>{e==="confirm"&&yl(),c()}})}catch{console.log("用户取消提交")}},vl=()=>({html:`
      <div class="confirm-data-container">
        <p class="confirm-title">请仔细检查以下信息是否正确：</p>
        <div class="confirm-item">
          <span class="label">商品名称：</span>
          <span class="value">${n.name||"未填写"}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品分类：</span>
          <span class="value">${_l(n.categoryId)||"未选择"}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品价格：</span>
          <span class="value">¥${n.price||0}</span>
          ${n.originalPrice?`<span class="original-price">（原价：¥${n.originalPrice}）</span>`:""}
        </div>
        <div class="confirm-item">
          <span class="label">库存数量：</span>
          <span class="value">${n.stockQuantity||0} 件</span>
        </div>
        ${n.tags?`
        <div class="confirm-item">
          <span class="label">商品标签：</span>
          <span class="value">${n.tags}</span>
        </div>
        `:""}
        ${n.color?`
        <div class="confirm-item">
          <span class="label">主要颜色：</span>
          <span class="value">${n.color}</span>
        </div>
        `:""}
        ${n.size?`
        <div class="confirm-item">
          <span class="label">规格描述：</span>
          <span class="value">${n.size}</span>
        </div>
        `:""}
        ${n.flowerLanguage?`
        <div class="confirm-item">
          <span class="label">花语寓意：</span>
          <span class="value">${n.flowerLanguage}</span>
        </div>
        `:""}
        ${n.occasion?`
        <div class="confirm-item">
          <span class="label">适用场合：</span>
          <span class="value">${n.occasion}</span>
        </div>
        `:""}
        <div class="confirm-item">
          <span class="label">是否精选：</span>
          <span class="value">${n.isFeatured===1?"是":"否"}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品状态：</span>
          <span class="value">${n.status===1?"上架":"下架"}</span>
        </div>
        <p class="confirm-warning">⚠️ 提交后将${B.value?"修改":"创建"}商品信息，请确认无误后点击确认按钮。</p>
      </div>
    `}),_l=s=>{const e=U.value.find(o=>o.id===s);return e?e.name:""},yl=async()=>{try{P.value=!0;const s={...n,detailImages:JSON.stringify(h.value)};console.log("提交的数据:",s),B.value?(console.log("更新商品，ID:",n.id),await O.updateFlower(n.id,s),d.success("商品修改成功！")):(console.log("创建新商品"),await O.createFlower(s),d.success("商品创建成功！")),E.value=!1,await T()}catch(s){console.error("提交失败:",s),d.error("提交失败: "+s.message)}finally{P.value=!1}},bl=()=>{const s=[...new Set(k.value.map(o=>o.color).filter(Boolean))],e=[...new Set(k.value.map(o=>o.size).filter(Boolean))];m.value.colors=s.map(o=>({label:o,value:o})),m.value.sizes=e.map(o=>({label:o,value:o}))},me=(s,e)=>{pa(s,["ID","商品名称","分类","价格","原价","库存","颜色","规格","状态","销量","创建时间"],e,v=>[v.id,v.name||"",v.categoryName||"",`¥${de(v.price)}`,`¥${de(v.originalPrice)}`,v.stock||0,v.color||"",v.size||"",Se(v.status),v.salesCount||0,Fe(v.createTime)])},hl=async s=>{try{switch(s){case"current":if(k.value.length===0){d.warning("当前页面没有商品数据");return}me(k.value,`商品数据_第${b.current}页_${new Date().toLocaleDateString()}.csv`),d.success(`已导出当前页 ${k.value.length} 条商品数据`);break;case"all":d.info("正在导出全部数据，请稍候...");try{const o=(await O.getFlowers({current:1,size:1e4,keyword:$.keyword,categoryId:$.categoryId,status:$.status})).data.records||[];if(o.length===0){d.warning("没有商品数据可导出");return}me(o,`全部商品数据_${new Date().toLocaleDateString()}.csv`),d.success(`全部商品数据导出完成，共 ${o.length} 条`)}catch(e){console.error("导出全部数据失败:",e),d.error("导出全部数据失败")}break;case"selected":if(w.value.length===0){d.warning("请先选择要导出的商品");return}me(w.value,`选中商品数据_${w.value.length}条_${new Date().toLocaleDateString()}.csv`),d.success(`已导出选中的 ${w.value.length} 条商品数据`);break}}catch(e){console.error("导出失败:",e),d.error("导出失败: "+e.message)}};return Rl(async()=>{console.log("商品管理页面已挂载，开始加载数据...");try{tl(),await F(),await G(),await T(),bl(),console.log("商品数据加载完成")}catch(s){console.error("商品数据加载失败:",s),d.error("商品数据加载失败: "+s.message)}}),(s,e)=>{const o=Be,c=Te,v=Ql,C=Al,V=Jl,j=Nl,ge=Zl,X=Hl,Y=Kl,q=ea,ne=Tl,wl=te("InfoFilled"),Cl=te("Money"),kl=te("Box"),Vl=te("CircleCheck"),Il=te("CircleClose"),I=la,xl=aa,le=oa,ae=sa,$l=na,zl=Ol,Sl=Ml,z=ia,re=ua,ie=ca,Ce=da,Fl=ra,Ul=Ll;return u(),_("div",Ia,[r("div",xa,[e[32]||(e[32]=r("h2",{class:"page-title"},"商品管理",-1)),l(c,{type:"primary",onClick:dl},{default:a(()=>[l(o,null,{default:a(()=>[l(g(_e))]),_:1}),e[31]||(e[31]=p(" 添加商品 ",-1))]),_:1,__:[31]})]),r("div",$a,[l(ne,{gutter:16},{default:a(()=>[l(C,{span:6},{default:a(()=>[l(v,{modelValue:$.keyword,"onUpdate:modelValue":e[0]||(e[0]=t=>$.keyword=t),placeholder:"搜索商品名称",clearable:"",onKeyup:jl(be,["enter"])},{prefix:a(()=>[l(o,null,{default:a(()=>[l(g(ke))]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(C,{span:4},{default:a(()=>[l(j,{modelValue:$.categoryId,"onUpdate:modelValue":e[1]||(e[1]=t=>$.categoryId=t),placeholder:"选择分类",clearable:""},{default:a(()=>[(u(!0),_(A,null,Q(U.value,t=>(u(),S(V,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(C,{span:4},{default:a(()=>[l(j,{modelValue:$.status,"onUpdate:modelValue":e[2]||(e[2]=t=>$.status=t),placeholder:"选择状态",clearable:""},{default:a(()=>[l(V,{label:"上架",value:1}),l(V,{label:"下架",value:0})]),_:1},8,["modelValue"])]),_:1}),l(C,{span:8},{default:a(()=>[l(c,{type:"primary",onClick:be},{default:a(()=>[l(o,null,{default:a(()=>[l(g(ke))]),_:1}),e[33]||(e[33]=p(" 搜索 ",-1))]),_:1,__:[33]}),l(c,{onClick:We},{default:a(()=>e[34]||(e[34]=[p(" 重置 ",-1)])),_:1,__:[34]}),l(c,{onClick:Ge,loading:Z.value},{default:a(()=>[l(o,null,{default:a(()=>[l(g(ql))]),_:1}),e[35]||(e[35]=p(" 刷新 ",-1))]),_:1,__:[35]},8,["loading"]),l(Y,{onCommand:hl,class:"export-dropdown"},{dropdown:a(()=>[l(X,null,{default:a(()=>[l(ge,{command:"current"},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Xl))]),_:1}),e[37]||(e[37]=p(" 导出当前页 ",-1))]),_:1,__:[37]}),l(ge,{command:"all"},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Ve))]),_:1}),e[38]||(e[38]=p(" 导出全部数据 ",-1))]),_:1,__:[38]}),l(ge,{command:"selected",divided:""},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Yl))]),_:1}),e[39]||(e[39]=p(" 导出已选择 ",-1))]),_:1,__:[39]})]),_:1})]),default:a(()=>[l(c,{size:"default",class:"export-btn"},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Wl))]),_:1}),e[36]||(e[36]=r("span",null,"导出",-1)),l(o,{class:"dropdown-icon"},{default:a(()=>[l(g(Gl))]),_:1})]),_:1,__:[36]})]),_:1})]),_:1}),l(C,{span:2},{default:a(()=>[r("div",za,[l(q,{type:"info",size:"small"},{default:a(()=>[p("共 "+f(b.total)+" 条",1)]),_:1})])]),_:1})]),_:1})]),w.value.length>0?(u(),_("div",Sa,[r("div",Fa,[r("div",Ua,[r("div",Ea,[l(o,null,{default:a(()=>[l(wl)]),_:1}),e[40]||(e[40]=r("span",{class:"summary-label"},"已选择:",-1)),r("span",Pa,f(w.value.length)+" 个商品",1)]),r("div",Da,[l(o,null,{default:a(()=>[l(Cl)]),_:1}),e[41]||(e[41]=r("span",{class:"summary-label"},"总金额:",-1)),r("span",Ba,"¥"+f(y.value.totalAmount),1)]),r("div",Ra,[l(o,null,{default:a(()=>[l(kl)]),_:1}),e[42]||(e[42]=r("span",{class:"summary-label"},"总库存:",-1)),r("span",Ta,f(y.value.totalStock),1)]),r("div",Oa,[l(o,null,{default:a(()=>[l(g(Ve))]),_:1}),e[43]||(e[43]=r("span",{class:"summary-label"},"分类:",-1)),r("span",Ma,f(y.value.categoryCount)+" 个",1)]),r("div",La,[l(o,null,{default:a(()=>[l(Vl)]),_:1}),e[44]||(e[44]=r("span",{class:"summary-label"},"上架:",-1)),r("span",Aa,f(y.value.onlineCount),1)]),r("div",Qa,[l(o,null,{default:a(()=>[l(Il)]),_:1}),e[45]||(e[45]=r("span",{class:"summary-label"},"下架:",-1)),r("span",ja,f(y.value.offlineCount),1)])])]),r("div",Na,[l(c,{type:"warning",size:"small",onClick:e[3]||(e[3]=t=>he(0)),disabled:y.value.onlineCount===0},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Ie))]),_:1}),p(" 批量下架 ("+f(y.value.onlineCount)+") ",1)]),_:1},8,["disabled"]),l(c,{type:"success",size:"small",onClick:e[4]||(e[4]=t=>he(1)),disabled:y.value.offlineCount===0},{default:a(()=>[l(o,null,{default:a(()=>[l(g(xe))]),_:1}),p(" 批量上架 ("+f(y.value.offlineCount)+") ",1)]),_:1},8,["disabled"]),l(c,{type:"danger",size:"small",onClick:ul},{default:a(()=>[l(o,null,{default:a(()=>[l(g(ye))]),_:1}),e[46]||(e[46]=p(" 批量删除 ",-1))]),_:1,__:[46]})])])):N("",!0),r("div",Ja,[l(zl,{data:k.value,loading:Z.value,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#606266"},"empty-text":"暂无商品数据",onSelectionChange:il},{default:a(()=>[l(I,{type:"selection",width:"55",align:"center"}),l(I,{label:"序号",width:"80",align:"center"},{default:a(({$index:t})=>[r("span",qa,f((b.current-1)*b.size+t+1),1)]),_:1}),l(I,{label:"商品图片",width:"120",align:"center"},{default:a(({row:t})=>[r("div",Ka,[l(xl,{src:t.mainImage,"preview-src-list":[t.mainImage],style:{width:"60px",height:"60px","border-radius":"8px"},fit:"cover","preview-teleported":!0},{error:a(()=>[r("div",Wa,[l(o,null,{default:a(()=>[l(g(ta))]),_:1})])]),_:2},1032,["src","preview-src-list"])])]),_:1}),l(I,{prop:"name",label:"商品名称","min-width":"200"},{default:a(({row:t})=>[r("div",Ga,[r("span",Ha,f(t.name),1),r("p",Za,f(t.description||"暂无描述"),1)])]),_:1}),l(I,{prop:"categoryName",label:"分类",width:"120",align:"center"},{header:a(()=>[r("div",Xa,[e[49]||(e[49]=r("span",null,"分类",-1)),l(Y,{onCommand:je,trigger:"click"},{dropdown:a(()=>[l(X,null,{default:a(()=>[r("div",Ya,[e[48]||(e[48]=r("div",{class:"filter-title"},"筛选分类",-1)),l(ae,{modelValue:i.value.category,"onUpdate:modelValue":e[5]||(e[5]=t=>i.value.category=t),onChange:R},{default:a(()=>[(u(!0),_(A,null,Q(m.value.categories,t=>(u(),S(le,{key:t.value,label:t.value},{default:a(()=>[p(f(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),r("div",et,[l(c,{size:"small",onClick:Oe},{default:a(()=>e[47]||(e[47]=[p("清空",-1)])),_:1,__:[47]})])])]),_:1})]),default:a(()=>[l(o,{class:"filter-icon"},{default:a(()=>[l(g(se))]),_:1})]),_:1})])]),default:a(({row:t})=>[l(q,{type:"primary",size:"small"},{default:a(()=>[p(f(t.categoryName),1)]),_:2},1024)]),_:1}),l(I,{prop:"price",label:"价格",width:"160",align:"center"},{header:a(()=>[r("div",lt,[e[52]||(e[52]=r("span",null,"价格",-1)),l(Y,{onCommand:Je,trigger:"click"},{dropdown:a(()=>[l(X,null,{default:a(()=>[r("div",at,[e[51]||(e[51]=r("div",{class:"filter-title"},"筛选价格",-1)),l(ae,{modelValue:i.value.priceRange,"onUpdate:modelValue":e[6]||(e[6]=t=>i.value.priceRange=t),onChange:R},{default:a(()=>[(u(!0),_(A,null,Q(m.value.priceRanges,t=>(u(),S(le,{key:t.label,label:t.value},{default:a(()=>[p(f(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),r("div",tt,[l(c,{size:"small",onClick:Le},{default:a(()=>e[50]||(e[50]=[p("清空",-1)])),_:1,__:[50]})])])]),_:1})]),default:a(()=>[l(o,{class:"filter-icon"},{default:a(()=>[l(g(se))]),_:1})]),_:1})])]),default:a(({row:t})=>[r("div",st,[r("div",ot,"¥"+f(g(de)(t.price)),1),t.originalPrice&&t.originalPrice>t.price?(u(),_("div",nt," 原价: ¥"+f(g(de)(t.originalPrice)),1)):N("",!0)])]),_:1}),l(I,{prop:"stockQuantity",label:"库存",width:"100",align:"center"},{header:a(()=>[r("div",rt,[e[55]||(e[55]=r("span",null,"库存",-1)),l(Y,{onCommand:qe,trigger:"click"},{dropdown:a(()=>[l(X,null,{default:a(()=>[r("div",it,[e[54]||(e[54]=r("div",{class:"filter-title"},"筛选库存",-1)),l(ae,{modelValue:i.value.stockRange,"onUpdate:modelValue":e[7]||(e[7]=t=>i.value.stockRange=t),onChange:R},{default:a(()=>[(u(!0),_(A,null,Q(m.value.stockRanges,t=>(u(),S(le,{key:t.label,label:t.value},{default:a(()=>[p(f(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),r("div",ut,[l(c,{size:"small",onClick:Ae},{default:a(()=>e[53]||(e[53]=[p("清空",-1)])),_:1,__:[53]})])])]),_:1})]),default:a(()=>[l(o,{class:"filter-icon"},{default:a(()=>[l(g(se))]),_:1})]),_:1})])]),default:a(({row:t})=>[l(q,{type:t.stockQuantity>10?"success":t.stockQuantity>0?"warning":"danger",size:"small"},{default:a(()=>[p(f(t.stockQuantity),1)]),_:2},1032,["type"])]),_:1}),l(I,{prop:"salesCount",label:"销量",width:"100",align:"center"},{default:a(({row:t})=>[l(q,{type:"info",size:"small"},{default:a(()=>[p(f(t.salesCount||0),1)]),_:2},1024)]),_:1}),l(I,{prop:"tags",label:"商品标签",width:"180",align:"center"},{default:a(({row:t})=>[r("div",dt,[(u(!0),_(A,null,Q(He(t.tags),H=>(u(),S(q,{key:H,size:"small",type:"info",style:{margin:"2px"}},{default:a(()=>[p(f(H),1)]),_:2},1024))),128)),t.tags?N("",!0):(u(),_("span",ct,"暂无标签"))])]),_:1}),l(I,{prop:"color",label:"颜色",width:"100",align:"center"},{header:a(()=>[r("div",pt,[e[58]||(e[58]=r("span",null,"颜色",-1)),l(Y,{onCommand:Ke,trigger:"click"},{dropdown:a(()=>[l(X,null,{default:a(()=>[r("div",ft,[e[57]||(e[57]=r("div",{class:"filter-title"},"筛选颜色",-1)),l(ae,{modelValue:i.value.color,"onUpdate:modelValue":e[8]||(e[8]=t=>i.value.color=t),onChange:R},{default:a(()=>[(u(!0),_(A,null,Q(m.value.colors,t=>(u(),S(le,{key:t.value,label:t.value},{default:a(()=>[r("div",mt,[r("div",{class:"color-dot",style:ue({backgroundColor:oe(t.label)})},null,4),p(" "+f(t.label),1)])]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),r("div",gt,[l(c,{size:"small",onClick:Qe},{default:a(()=>e[56]||(e[56]=[p("清空",-1)])),_:1,__:[56]})])])]),_:1})]),default:a(()=>[l(o,{class:"filter-icon"},{default:a(()=>[l(g(se))]),_:1})]),_:1})])]),default:a(({row:t})=>[t.color?(u(),_("div",vt,[r("div",{class:"color-dot",style:ue({backgroundColor:oe(t.color)})},null,4),r("span",_t,f(t.color),1)])):(u(),_("span",yt,"未设置"))]),_:1}),l(I,{prop:"size",label:"规格",width:"100",align:"center"},{default:a(({row:t})=>[t.size?(u(),S(q,{key:0,type:"warning",size:"small"},{default:a(()=>[p(f(t.size),1)]),_:2},1024)):(u(),_("span",bt,"未设置"))]),_:1}),l(I,{prop:"isFeatured",label:"精选",width:"80",align:"center"},{default:a(({row:t})=>[l(q,{type:t.isFeatured===1?"success":"info",size:"small"},{default:a(()=>[p(f(t.isFeatured===1?"精选":"普通"),1)]),_:2},1032,["type"])]),_:1}),l(I,{prop:"flowerLanguage",label:"花语寓意",width:"150",align:"center"},{default:a(({row:t})=>[r("div",ht,[t.flowerLanguage?(u(),_("span",wt,f(t.flowerLanguage),1)):(u(),_("span",Ct,"暂无花语"))])]),_:1}),l(I,{prop:"occasion",label:"适用场合",width:"120",align:"center"},{default:a(({row:t})=>[r("div",kt,[t.occasion?(u(),_("span",Vt,f(t.occasion),1)):(u(),_("span",It,"未设置"))])]),_:1}),l(I,{prop:"careInstructions",label:"养护说明",width:"120",align:"center"},{default:a(({row:t})=>[r("div",xt,[t.careInstructions?(u(),S($l,{key:0,content:t.careInstructions,placement:"top","show-after":500},{default:a(()=>[r("span",$t,f(Xe(t.careInstructions,10)),1)]),_:2},1032,["content"])):(u(),_("span",zt,"暂无说明"))])]),_:1}),l(I,{prop:"status",label:"状态",width:"100",align:"center"},{header:a(()=>[r("div",St,[e[61]||(e[61]=r("span",null,"状态",-1)),l(Y,{onCommand:Ne,trigger:"click"},{dropdown:a(()=>[l(X,null,{default:a(()=>[r("div",Ft,[e[60]||(e[60]=r("div",{class:"filter-title"},"筛选状态",-1)),l(ae,{modelValue:i.value.status,"onUpdate:modelValue":e[9]||(e[9]=t=>i.value.status=t),onChange:R},{default:a(()=>[(u(!0),_(A,null,Q(m.value.statuses,t=>(u(),S(le,{key:t.value,label:t.value},{default:a(()=>[p(f(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),r("div",Ut,[l(c,{size:"small",onClick:Me},{default:a(()=>e[59]||(e[59]=[p("清空",-1)])),_:1,__:[59]})])])]),_:1})]),default:a(()=>[l(o,{class:"filter-icon"},{default:a(()=>[l(g(se))]),_:1})]),_:1})])]),default:a(({row:t})=>[l(q,{type:t.status===1?"success":"danger",size:"small"},{default:a(()=>[p(f(g(Se)(t.status)),1)]),_:2},1032,["type"])]),_:1}),l(I,{prop:"createdAt",label:"创建时间",width:"180",align:"center"},{default:a(({row:t})=>[r("span",Et,f(g(Fe)(t.createdAt)),1)]),_:1}),l(I,{label:"操作",width:"280",fixed:"right",align:"center"},{default:a(({row:t})=>[r("div",Pt,[l(c,{type:"primary",size:"small",onClick:H=>cl(t)},{default:a(()=>[l(o,null,{default:a(()=>[l(g(Re))]),_:1}),e[62]||(e[62]=p(" 编辑 ",-1))]),_:2,__:[62]},1032,["onClick"]),l(c,{type:t.status===1?"warning":"success",size:"small",onClick:H=>nl(t)},{default:a(()=>[t.status===1?(u(),S(o,{key:0},{default:a(()=>[l(g(Ie))]),_:1})):(u(),S(o,{key:1},{default:a(()=>[l(g(xe))]),_:1})),p(" "+f(t.status===1?"下架":"上架"),1)]),_:2},1032,["type","onClick"]),l(c,{type:"danger",size:"small",onClick:H=>rl(t)},{default:a(()=>[l(o,null,{default:a(()=>[l(g(ye))]),_:1}),e[63]||(e[63]=p(" 删除 ",-1))]),_:2,__:[63]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"])]),r("div",Dt,[l(Sl,{"current-page":b.current,"onUpdate:currentPage":e[10]||(e[10]=t=>b.current=t),"page-size":b.size,"onUpdate:pageSize":e[11]||(e[11]=t=>b.size=t),"page-sizes":[10,20,50,100],total:b.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:sl,onCurrentChange:ol},null,8,["current-page","page-size","total"])]),l(Ul,{modelValue:E.value,"onUpdate:modelValue":e[30]||(e[30]=t=>E.value=t),title:B.value?"编辑商品":"添加商品",width:"800px","close-on-click-modal":!1},{footer:a(()=>[r("div",At,[l(c,{onClick:e[29]||(e[29]=t=>E.value=!1)},{default:a(()=>e[68]||(e[68]=[p("取消",-1)])),_:1,__:[68]}),l(c,{type:"primary",loading:P.value,onClick:ml},{default:a(()=>[p(f(B.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:a(()=>[l(Fl,{ref_key:"formRef",ref:D,model:n,rules:ce,"label-width":"120px"},{default:a(()=>[l(z,{label:"商品名称",prop:"name"},{default:a(()=>[l(v,{modelValue:n.name,"onUpdate:modelValue":e[12]||(e[12]=t=>n.name=t),placeholder:"请输入商品名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(z,{label:"商品分类",prop:"categoryId"},{default:a(()=>[l(j,{modelValue:n.categoryId,"onUpdate:modelValue":e[13]||(e[13]=t=>n.categoryId=t),placeholder:"请选择商品分类",style:{width:"100%"}},{default:a(()=>[(u(!0),_(A,null,Q(U.value,t=>(u(),S(V,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(z,{label:"商品价格",prop:"price"},{default:a(()=>[l(re,{modelValue:n.price,"onUpdate:modelValue":e[14]||(e[14]=t=>n.price=t),min:0,precision:2,placeholder:"请输入商品价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(z,{label:"库存数量",prop:"stockQuantity"},{default:a(()=>[l(re,{modelValue:n.stockQuantity,"onUpdate:modelValue":e[15]||(e[15]=t=>n.stockQuantity=t),min:0,placeholder:"请输入库存数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(z,{label:"商品描述",prop:"description"},{default:a(()=>[l(v,{modelValue:n.description,"onUpdate:modelValue":e[16]||(e[16]=t=>n.description=t),type:"textarea",rows:4,placeholder:"请输入商品描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(z,{label:"主图片",prop:"mainImage"},{default:a(()=>[l(Ee,{type:"main",modelValue:n.mainImage,"onUpdate:modelValue":e[17]||(e[17]=t=>n.mainImage=t),onChange:pl},null,8,["modelValue"])]),_:1}),l(z,{label:"详情图片"},{default:a(()=>[l(Ee,{type:"detail",modelValue:h.value,"onUpdate:modelValue":e[18]||(e[18]=t=>h.value=t),"max-count":10,onChange:fl},null,8,["modelValue"])]),_:1}),l(ne,{gutter:20},{default:a(()=>[l(C,{span:12},{default:a(()=>[l(z,{label:"原价",prop:"originalPrice"},{default:a(()=>[l(re,{modelValue:n.originalPrice,"onUpdate:modelValue":e[19]||(e[19]=t=>n.originalPrice=t),min:0,precision:2,placeholder:"请输入原价",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(C,{span:12},{default:a(()=>[l(z,{label:"销售数量",prop:"salesCount"},{default:a(()=>[l(re,{modelValue:n.salesCount,"onUpdate:modelValue":e[20]||(e[20]=t=>n.salesCount=t),min:0,placeholder:"销售数量",style:{width:"100%"},disabled:!0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(z,{label:"商品标签",prop:"tags"},{default:a(()=>[l(v,{modelValue:n.tags,"onUpdate:modelValue":e[21]||(e[21]=t=>n.tags=t),placeholder:"请输入标签，用逗号分隔，如：浪漫,爱情,表白",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(z,{label:"花语寓意",prop:"flowerLanguage"},{default:a(()=>[l(v,{modelValue:n.flowerLanguage,"onUpdate:modelValue":e[22]||(e[22]=t=>n.flowerLanguage=t),placeholder:"请输入花语寓意",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(z,{label:"养护说明",prop:"careInstructions"},{default:a(()=>[l(v,{modelValue:n.careInstructions,"onUpdate:modelValue":e[23]||(e[23]=t=>n.careInstructions=t),type:"textarea",rows:3,placeholder:"请输入养护说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(ne,{gutter:20},{default:a(()=>[l(C,{span:8},{default:a(()=>[l(z,{label:"适用场合",prop:"occasion"},{default:a(()=>[l(v,{modelValue:n.occasion,"onUpdate:modelValue":e[24]||(e[24]=t=>n.occasion=t),placeholder:"如：情人节,表白,纪念日",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1}),l(C,{span:8},{default:a(()=>[l(z,{label:"主要颜色",prop:"color"},{default:a(()=>[r("div",Bt,[l(j,{modelValue:n.color,"onUpdate:modelValue":e[25]||(e[25]=t=>n.color=t),placeholder:"请选择或输入颜色",style:{width:"100%"},filterable:"","allow-create":"",onChange:Ye},{default:a(()=>[(u(!0),_(A,null,Q(M.value,t=>(u(),S(V,{key:t,label:t,value:t},{default:a(()=>[r("div",Rt,[r("div",{class:"color-preview",style:ue({backgroundColor:oe(t)})},null,4),r("span",Tt,f(t),1),pe(t)?(u(),S(o,{key:0,class:"delete-icon",onClick:$e(H=>ll(t),["stop"])},{default:a(()=>[l(g(ze))]),_:2},1032,["onClick"])):N("",!0)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),n.color?(u(),_("div",{key:0,class:"selected-color-preview",style:ue({backgroundColor:oe(n.color)}),title:n.color},null,12,Ot)):N("",!0)])]),_:1})]),_:1}),l(C,{span:8},{default:a(()=>[l(z,{label:"规格描述",prop:"size"},{default:a(()=>[l(j,{modelValue:n.size,"onUpdate:modelValue":e[26]||(e[26]=t=>n.size=t),placeholder:"请选择或输入规格",style:{width:"100%"},filterable:"","allow-create":"",onChange:el},{default:a(()=>[(u(!0),_(A,null,Q(L.value,t=>(u(),S(V,{key:t,label:t,value:t},{default:a(()=>[r("div",Mt,[r("span",Lt,f(t),1),fe(t)?(u(),S(o,{key:0,class:"delete-icon",onClick:$e(H=>al(t),["stop"])},{default:a(()=>[l(g(ze))]),_:2},1032,["onClick"])):N("",!0)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(ne,{gutter:20},{default:a(()=>[l(C,{span:12},{default:a(()=>[l(z,{label:"是否精选",prop:"isFeatured"},{default:a(()=>[l(Ce,{modelValue:n.isFeatured,"onUpdate:modelValue":e[27]||(e[27]=t=>n.isFeatured=t)},{default:a(()=>[l(ie,{label:1},{default:a(()=>e[64]||(e[64]=[p("精选",-1)])),_:1,__:[64]}),l(ie,{label:0},{default:a(()=>e[65]||(e[65]=[p("普通",-1)])),_:1,__:[65]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(C,{span:12},{default:a(()=>[l(z,{label:"商品状态",prop:"status"},{default:a(()=>[l(Ce,{modelValue:n.status,"onUpdate:modelValue":e[28]||(e[28]=t=>n.status=t)},{default:a(()=>[l(ie,{label:1},{default:a(()=>e[66]||(e[66]=[p("上架",-1)])),_:1,__:[66]}),l(ie,{label:0},{default:a(()=>e[67]||(e[67]=[p("下架",-1)])),_:1,__:[67]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},rs=Pe(Qt,[["__scopeId","data-v-ce8e0525"]]);export{rs as default};
