import{_ as F}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                     *//* empty css                    *//* empty css                 */import{u as L,r as i,a as U,o as q,c as p,b as r,d as o,w as t,e as z,E as B,f as R,g,h as N,i as _,j as S,k as A,l as K,m as M,n as P,p as T,q as w,s as j,t as D}from"./index-Dzimn2-8.js";const G={class:"login-container"},H={class:"login-box"},J={class:"captcha-container"},O=["src"],Q={key:1,class:"captcha-loading"},W={__name:"Login",setup(X){const C=N(),V=L(),d=i(),c=i(!1),u=i(""),f=i(""),a=U({username:"admin",password:"123456",captchaCode:"",remember:!1}),x={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],captchaCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:4,message:"验证码长度为4位",trigger:"blur"}]},h=async()=>{try{const s=await R.getCaptcha();s.data&&(f.value=s.data.captchaId,u.value=s.data.image)}catch(s){console.error("获取验证码失败:",s),g.error("获取验证码失败")}},v=()=>{a.captchaCode="",h()},b=async()=>{if(d.value)try{await d.value.validate(),c.value=!0,await V.login({username:a.username,password:a.password,captchaId:f.value,captchaCode:a.captchaCode,remember:a.remember}),g.success("登录成功"),await new Promise(s=>setTimeout(s,100)),await C.replace("/")}catch(s){console.error("Login error:",s),g.error(s.message||"登录失败"),v()}finally{c.value=!1}};return q(()=>{h()}),(s,e)=>{const m=A,n=S,y=K,k=T,E=j,I=B;return _(),p("div",G,[r("div",H,[e[6]||(e[6]=r("div",{class:"login-header"},[r("h1",null,"花语小铺"),r("p",null,"管理后台登录")],-1)),o(I,{ref_key:"loginFormRef",ref:d,model:a,rules:x,class:"login-form",onKeyup:z(b,["enter"])},{default:t(()=>[o(n,{prop:"username"},{default:t(()=>[o(m,{modelValue:a.username,"onUpdate:modelValue":e[0]||(e[0]=l=>a.username=l),placeholder:"请输入用户名",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),o(n,{prop:"password"},{default:t(()=>[o(m,{modelValue:a.password,"onUpdate:modelValue":e[1]||(e[1]=l=>a.password=l),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),o(n,{prop:"captchaCode"},{default:t(()=>[r("div",J,[o(m,{modelValue:a.captchaCode,"onUpdate:modelValue":e[2]||(e[2]=l=>a.captchaCode=l),placeholder:"请输入验证码",size:"large","prefix-icon":"Picture",clearable:"",class:"captcha-input"},null,8,["modelValue"]),r("div",{class:"captcha-image-container",onClick:v},[u.value?(_(),p("img",{key:0,src:u.value,alt:"验证码",class:"captcha-image"},null,8,O)):(_(),p("div",Q,[o(y,null,{default:t(()=>[o(M(P))]),_:1})])),e[4]||(e[4]=r("div",{class:"captcha-refresh-hint"},"点击刷新",-1))])])]),_:1}),o(n,null,{default:t(()=>[o(k,{modelValue:a.remember,"onUpdate:modelValue":e[3]||(e[3]=l=>a.remember=l)},{default:t(()=>e[5]||(e[5]=[w("记住我",-1)])),_:1,__:[5]},8,["modelValue"])]),_:1}),o(n,null,{default:t(()=>[o(E,{type:"primary",size:"large",loading:c.value,onClick:b,class:"login-button"},{default:t(()=>[w(D(c.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),e[7]||(e[7]=r("div",{class:"login-footer"},[r("p",null,"妙星猫出品，必属精品 ")],-1))])])}}},oe=F(W,[["__scopeId","data-v-3de8513a"]]);export{oe as default};
