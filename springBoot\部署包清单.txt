花店后端项目部署包清单
====================

项目信息:
- 项目名称: flower-shop
- 版本: 1.0.0
- 服务器地址: www.mxm.qiangs.xyz
- 端口: 8080
- 访问路径: /api

部署文件列表:
====================

1. 核心文件:
   ✓ flower-shop-1.0.0.jar          - 主程序JAR包
   ✓ application.yml                 - 默认配置文件
   ✓ application-prod.yml            - 生产环境配置文件

2. 部署脚本:
   ✓ deploy.sh                       - Linux部署脚本
   ✓ deploy.bat                      - Windows部署脚本

3. 文档:
   ✓ 部署说明.md                     - 详细部署说明文档
   ✓ 部署包清单.txt                  - 本文件

文件路径:
====================
项目根目录: c:\Users\<USER>\Desktop\flower\springBoot\
JAR文件位置: c:\Users\<USER>\Desktop\flower\springBoot\target\flower-shop-1.0.0.jar

需要上传到服务器的文件:
====================
1. target/flower-shop-1.0.0.jar
2. deploy.sh (Linux) 或 deploy.bat (Windows)
3. 部署说明.md

服务器配置参数:
====================
- 服务器地址: www.mxm.qiangs.xyz
- 端口: 8080
- 完整API地址: http://www.mxm.qiangs.xyz:8080/api
- 数据库: MySQL 8.0+
- 数据库名: flower_shop
- 数据库用户: flower_shop
- 数据库密码: mxm_flowers
- Java版本: JDK 17+

快速部署命令:
====================
Linux:
  chmod +x deploy.sh
  ./deploy.sh

Windows:
  deploy.bat

手动启动命令:
====================
生产环境:
  java -jar flower-shop-1.0.0.jar --spring.profiles.active=prod

后台运行:
  nohup java -jar flower-shop-1.0.0.jar --spring.profiles.active=prod > logs/app.log 2>&1 &

测试接口:
====================
健康检查: GET http://www.mxm.qiangs.xyz:8080/api/test/health
数据库测试: GET http://www.mxm.qiangs.xyz:8080/api/test/db

注意事项:
====================
1. 确保服务器已安装Java 17+
2. 确保MySQL服务正在运行
3. 确保8080端口未被占用
4. 确保防火墙已开放8080端口
5. 建议在生产环境中修改数据库密码
6. 建议配置域名解析指向服务器IP

部署完成标志:
====================
当看到以下日志时，表示部署成功:
"花语小铺后端服务启动成功！"
"API接口地址: http://www.mxm.qiangs.xyz:8080/api"
