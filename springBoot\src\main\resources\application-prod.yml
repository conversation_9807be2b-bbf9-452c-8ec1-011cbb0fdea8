server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: flower-shop

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 生产环境数据库配置
    url: *****************************************************************************************************************************************************
    username: flower_shop
    password: mxm_flowers

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 微信小程序配置
wechat:
  miniprogram:
    # 生产环境请替换为真实的AppID和AppSecret
    app-id: your_app_id_here
    app-secret: your_app_secret_here

# JWT配置
jwt:
  secret: flower-admin-secret-key-2025-prod
  expiration: 86400  # 24小时

# 应用配置
app:
  # 服务器配置
  server:
    # 生产环境服务器地址
    base-url: https://www.mxm.qiangs.xyz:8080
    # 图片访问路径前缀
    image-url-prefix: ${app.server.base-url}/api

# 文件上传配置
file:
  upload:
    # 轮播图上传路径
    swiper-path: src/main/resources/image/swiper/
    # 图片访问URL前缀
    image-url-prefix: /image/swiper/
    # 服务器基础URL
    server-base-url: http://www.mxm.qiangs.xyz:8080/api
    # 允许的图片格式
    allowed-image-types:
      - jpg
      - jpeg
      - png
      - gif
    # 最大文件大小（字节）2MB
    max-file-size: 2097152
    
# Logging Configuration - 生产环境配置
logging:
  level:
    com.flower: info
    org.springframework.web: warn
    root: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/flower-shop.log
    max-size: 100MB
    max-history: 30
