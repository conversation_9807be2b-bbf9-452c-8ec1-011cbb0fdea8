import{bs as f}from"./index-BiIMV38A.js";const p=(t,e="YYYY-MM-DD HH:mm:ss")=>t?f(t).format(e):"",b=(t,e=2)=>t==null?"0.00":Number(t).toFixed(e),m=t=>({1:"待付款",2:"已下单",3:"配送中",4:"已完成",5:"已取消"})[t]||"未知状态",y=t=>({1:"warning",2:"primary",3:"info",4:"success",5:"danger"})[t]||"info",g=t=>t===1?"正常":"禁用",h=t=>t===1?"上架":"下架",v=(t,e,c,n)=>{if(!t||t.length===0)throw new Error("没有数据可导出");const a=t.map(r=>n?n(r):Object.values(r)),u=[e,...a].map(r=>r.map(d=>`"${d||""}"`).join(",")).join(`
`),i=new Blob(["\uFEFF"+u],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),o=URL.createObjectURL(i);s.setAttribute("href",o),s.setAttribute("download",c),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o)};export{m as a,p as b,g as c,h as d,v as e,b as f,y as g};
