# 花语小铺前端项目 - 故障排查指南

## 🚨 当前问题分析

### 问题描述
- ✅ 前端页面能正常显示
- ❌ API接口无法访问
- ❌ 数据无法加载

### 根本原因
1. **Nginx配置问题**：location配置冲突，API代理不生效
2. **根目录路径错误**：配置中使用了错误的网站根目录
3. **环境变量配置问题**：API地址配置不正确

## 🔧 解决步骤

### 第一步：修正Nginx配置

**问题配置**：
```nginx
root /www/wwwroot/flower_vue;  # ❌ 错误路径
```

**正确配置**：
```nginx
root /www/wwwroot/www.mxm.qiangs.xyz;  # ✅ 正确路径
```

**完整的正确Nginx配置**：
```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name www.mxm.qiangs.xyz;
    index index.html;
    root /www/wwwroot/www.mxm.qiangs.xyz;

    # HTTPS重定向
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    # SSL配置
    ssl_certificate    /www/server/panel/vhost/cert/www.mxm.qiangs.xyz/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/www.mxm.qiangs.xyz/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # API代理配置 - 关键
    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 图片代理配置
    location /image/ {
        proxy_pass http://127.0.0.1:8080/image/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        try_files $uri =404;
    }

    # SPA路由支持 - 必须放在最后
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全配置
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404;
    }

    # SSL证书验证
    location ~ \.well-known{
        allow all;
    }

    access_log  /www/wwwlogs/www.mxm.qiangs.xyz.log;
    error_log  /www/wwwlogs/www.mxm.qiangs.xyz.error.log;
}
```

### 第二步：检查文件上传

1. **确认网站根目录**：
```bash
ls -la /www/wwwroot/www.mxm.qiangs.xyz/
```

应该看到：
```
index.html
assets/
notification-sound.mp3
image-upload-test.html
```

2. **如果文件在错误目录**：
```bash
# 移动文件到正确目录
mv /www/wwwroot/flower_vue/* /www/wwwroot/www.mxm.qiangs.xyz/
```

### 第三步：检查后端服务

1. **检查后端是否运行**：
```bash
netstat -tlnp | grep :8080
# 或
ss -tlnp | grep :8080
```

2. **如果后端未运行**，启动后端服务：
```bash
cd /path/to/your/springboot/project
java -jar flower-shop-1.0.0.jar --spring.profiles.active=prod
```

### 第四步：重新上传前端文件

由于修改了配置并重新打包，需要重新上传：

1. **删除旧文件**：
```bash
rm -rf /www/wwwroot/www.mxm.qiangs.xyz/*
```

2. **上传新的dist文件**：
将 `Vue/dist/` 目录下的所有文件上传到 `/www/wwwroot/www.mxm.qiangs.xyz/`

### 第五步：重启服务

```bash
# 重启Nginx
systemctl restart nginx

# 或在宝塔面板中重启Nginx
```

## 🔍 验证步骤

### 1. 基础验证
```bash
# 检查Nginx配置语法
nginx -t

# 检查网站文件
ls -la /www/wwwroot/www.mxm.qiangs.xyz/

# 检查端口监听
netstat -tlnp | grep -E ':(80|443|8080)'
```

### 2. 浏览器验证

1. **访问前端**：https://www.mxm.qiangs.xyz
2. **打开开发者工具**（F12）
3. **查看Network标签**
4. **尝试登录**，观察API请求

### 3. API测试

使用curl测试API：
```bash
# 测试API连接
curl -k https://www.mxm.qiangs.xyz/api/test/health

# 测试后端直连
curl http://127.0.0.1:8080/api/test/health
```

## 🚨 常见错误及解决

### 错误1：502 Bad Gateway
**原因**：后端服务未启动或端口不对
**解决**：
```bash
# 检查后端服务
ps aux | grep java
netstat -tlnp | grep :8080
```

### 错误2：404 Not Found (API请求)
**原因**：Nginx代理配置错误
**解决**：检查location /api/配置是否正确

### 错误3：CORS错误
**原因**：跨域配置问题
**解决**：在Nginx中添加CORS头（已包含在上面的配置中）

### 错误4：静态资源404
**原因**：文件路径或location配置问题
**解决**：检查root路径和文件是否存在

## 📋 检查清单

- [ ] Nginx配置已更新并重启
- [ ] 网站根目录路径正确：`/www/wwwroot/www.mxm.qiangs.xyz`
- [ ] 前端文件已正确上传到根目录
- [ ] 后端服务在8080端口正常运行
- [ ] 防火墙已开放80、443、8080端口
- [ ] SSL证书正常工作
- [ ] 浏览器能访问前端页面
- [ ] API请求能正常响应

## 📞 获取帮助

### 查看日志
```bash
# Nginx访问日志
tail -f /www/wwwlogs/www.mxm.qiangs.xyz.log

# Nginx错误日志
tail -f /www/wwwlogs/www.mxm.qiangs.xyz.error.log

# 后端应用日志
tail -f logs/flower-shop.log
```

### 测试命令
```bash
# 测试域名解析
nslookup www.mxm.qiangs.xyz

# 测试端口连通性
telnet 127.0.0.1 8080

# 测试SSL证书
openssl s_client -connect www.mxm.qiangs.xyz:443
```

---
**重要提醒**：按照以上步骤操作后，API接口应该能正常访问。如果仍有问题，请检查后端服务是否正常运行。
