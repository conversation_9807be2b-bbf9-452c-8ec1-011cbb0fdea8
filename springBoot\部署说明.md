# 花店后端项目部署说明

## 项目信息
- **项目名称**: flower-shop
- **版本**: 1.0.0
- **服务器地址**: www.mxm.qiangs.xyz
- **端口**: 8080
- **访问路径**: /api

## 打包文件位置
```
项目路径: c:\Users\<USER>\Desktop\flower\springBoot\
JAR文件: c:\Users\<USER>\Desktop\flower\springBoot\target\flower-shop-1.0.0.jar
```

## 服务器环境要求

### 1. Java环境
- **Java版本**: JDK 17 或以上
- **验证命令**: `java -version`

### 2. 数据库环境
- **数据库**: MySQL 8.0+
- **数据库名**: flower_shop
- **用户名**: flower_shop
- **密码**: mxm_flowers
- **端口**: 3306

### 3. 系统要求
- **操作系统**: Linux/Windows
- **内存**: 建议2GB以上
- **磁盘空间**: 建议1GB以上

## 部署步骤

### 1. 上传文件到服务器
将以下文件上传到服务器：
```
flower-shop-1.0.0.jar
deploy.sh
```

### 2. 设置执行权限（Linux）
```bash
chmod +x deploy.sh
```

### 3. 运行部署脚本
```bash
./deploy.sh
```

### 4. 手动启动（可选）
如果不使用脚本，可以手动启动：
```bash
# 生产环境启动
java -jar flower-shop-1.0.0.jar --spring.profiles.active=prod

# 后台运行
nohup java -jar flower-shop-1.0.0.jar --spring.profiles.active=prod > logs/app.log 2>&1 &
```

## 配置参数

### 1. 服务器配置
- **基础URL**: http://www.mxm.qiangs.xyz:8080
- **API前缀**: /api
- **完整访问地址**: http://www.mxm.qiangs.xyz:8080/api

### 2. 数据库配置
生产环境数据库配置在 `application-prod.yml` 中：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: flower_shop
    password: mxm_flowers
```

### 3. 文件上传配置
- **图片访问前缀**: http://www.mxm.qiangs.xyz:8080/api/image/
- **上传路径**: src/main/resources/image/
- **最大文件大小**: 10MB

### 4. 日志配置
- **日志文件**: logs/flower-shop.log
- **日志级别**: INFO
- **日志保留**: 30天
- **单文件大小**: 100MB

## 常用命令

### 1. 查看运行状态
```bash
ps -ef | grep flower-shop
```

### 2. 查看日志
```bash
tail -f logs/flower-shop.log
```

### 3. 停止应用
```bash
# 查找进程ID
ps -ef | grep flower-shop-1.0.0.jar | grep -v grep

# 停止进程（替换PID为实际进程ID）
kill -9 PID
```

### 4. 重启应用
```bash
./deploy.sh
```

## 防火墙配置

### Linux (iptables)
```bash
# 开放8080端口
iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
```

### Linux (firewalld)
```bash
# 开放8080端口
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload
```

### Windows
在Windows防火墙中添加入站规则，开放8080端口。

## 测试接口

### 1. 健康检查
```
GET http://www.mxm.qiangs.xyz:8080/api/test/health
```

### 2. 数据库连接测试
```
GET http://www.mxm.qiangs.xyz:8080/api/test/db
```

## 数据库准备

在部署前，需要在MySQL中创建数据库和用户：

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS flower_shop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'flower_shop'@'localhost' IDENTIFIED BY 'mxm_flowers';
GRANT ALL PRIVILEGES ON flower_shop.* TO 'flower_shop'@'localhost';
FLUSH PRIVILEGES;
```

## 注意事项

1. **数据库连接**: 确保MySQL服务正在运行，并且数据库 `flower_shop` 已创建，用户 `flower_shop` 已配置
2. **端口占用**: 确保8080端口未被其他应用占用
3. **文件权限**: 确保应用有权限读写日志目录和图片目录
4. **内存配置**: 如果服务器内存较小，可以添加JVM参数：
   ```bash
   java -Xms512m -Xmx1024m -jar flower-shop-1.0.0.jar --spring.profiles.active=prod
   ```

## 故障排查

### 1. 启动失败
- 检查Java版本是否正确
- 检查端口是否被占用
- 查看启动日志：`cat logs/startup.log`

### 2. 数据库连接失败
- 检查MySQL服务状态
- 验证数据库连接参数
- 检查防火墙设置

### 3. 接口访问失败
- 检查防火墙端口开放
- 验证服务器IP和域名解析
- 查看应用日志

## 联系信息
如有问题，请检查日志文件或联系技术支持。
