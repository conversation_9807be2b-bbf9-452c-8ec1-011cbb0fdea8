import{_ as xe}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                    *//* empty css                        *//* empty css               */import{u as Ee,r as w,a as R,y as P,o as Ve,g as r,c as $,b as i,G as q,d as t,w as l,s as ze,aC as Se,l as $e,t as _,aD as Be,aJ as Oe,a1 as Ie,f as k,ar as O,i as v,q as m,m as d,aO as J,aE as Te,k as Re,e as Pe,N as K,aK as De,aL as Ae,aT as Me,b4 as Ne,b5 as Fe,b6 as D,b7 as A,aR as M,aH as Ge,B as I,aZ as je,ap as H,P as Le,aP as qe,E as Je,j as Ke,aQ as He,b0 as Qe,aF as Ze,b3 as We}from"./index-BSEuU4Y2.js";import{b as Xe}from"./index-B9v4HaLW.js";const Ye={class:"page-container"},et={class:"page-header"},tt={class:"search-bar mb-16"},at={key:0,class:"batch-actions"},lt={class:"batch-info"},st={class:"batch-summary"},ot={class:"summary-item"},nt={class:"summary-value"},rt={class:"summary-item"},it={class:"summary-value"},ut={class:"summary-item"},dt={class:"summary-value"},ct={class:"summary-item"},mt={class:"summary-value"},pt={class:"batch-buttons"},gt={class:"row-number"},_t={class:"image-error"},ft={key:1,class:"no-image"},vt={class:"action-buttons"},bt={class:"pagination-container"},yt={class:"image-upload-container"},Ct=["src"],ht={key:0,class:"image-actions"},wt={__name:"Categories",setup(kt){const Q=Ee(),B=w(!1),T=w(!1),U=w([]),p=w([]),x=w(!1),E=w(!1),V=w(),b=R({keyword:"",status:null}),u=R({current:1,size:10,total:0}),o=R({id:null,name:"",description:"",imageUrl:"",sortOrder:0,status:1}),Z={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:50,message:"分类名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{max:200,message:"描述不能超过 200 个字符",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序值",trigger:"blur"}]},W=P(()=>"http://localhost:8080/api/admin/upload/image"),X=P(()=>({Authorization:`Bearer ${Q.token}`})),C=P(()=>{if(p.value.length===0)return{sortRange:"-",enabledCount:0,disabledCount:0};const a=p.value.map(y=>y.sortOrder||0),e=Math.min(...a),s=Math.max(...a),c=e===s?e.toString():`${e}-${s}`,h=p.value.filter(y=>y.status===1).length,z=p.value.filter(y=>y.status===0).length;return{sortRange:c,enabledCount:h,disabledCount:z}}),g=async()=>{B.value=!0;try{const a={current:u.current,size:u.size,keyword:b.keyword,status:b.status};Object.keys(a).forEach(s=>{(a[s]===null||a[s]===void 0||a[s]==="")&&delete a[s]}),console.log("请求参数:",a);const e=await k.getCategories(a);e&&e.data?(U.value=e.data.records||[],u.total=e.data.total||0,console.log("分类数据:",{records:U.value.length,total:u.total,current:u.current,size:u.size})):(U.value=[],u.total=0)}catch(a){console.error("加载分类列表失败:",a),console.error("错误详情:",a.response||a),r.error("加载分类列表失败: "+(a.message||"未知错误")),U.value=[],u.total=0}finally{B.value=!1}},N=()=>{u.current=1,g()},Y=()=>{b.keyword="",b.status=null,u.current=1,g(),r.success("搜索条件已重置")},ee=()=>{g(),r({message:"分类数据已刷新",type:"success",duration:1500,showClose:!1})},te=a=>{u.size=a,u.current=1,g()},ae=a=>{u.current=a,g()},le=()=>{E.value=!1,F(),x.value=!0},se=a=>{console.log("=== 打开编辑对话框 ==="),console.log("原始分类数据:",a),E.value=!0,F(),o.id=a.id,o.name=a.name,o.description=a.description||"",o.imageUrl=a.imageUrl||"",o.sortOrder=a.sortOrder||0,o.status=a.status!==void 0?a.status:1,console.log("设置后的表单数据:",{...o}),x.value=!0},F=()=>{o.id=null,o.name="",o.description="",o.imageUrl="",o.sortOrder=0,o.status=1,V.value&&V.value.clearValidate()},oe=a=>{if(console.log("图片上传响应:",a),a.code===200){const e=o.imageUrl;o.imageUrl=a.data.url,console.log("图片上传成功!"),console.log("旧图片URL:",e),console.log("新图片URL:",o.imageUrl),console.log("当前表单数据:",{...o}),r.success("图片上传成功")}else console.error("图片上传失败:",a),r.error(a.message||"图片上传失败")},ne=a=>{console.error("图片上传失败:",a),r.error("图片上传失败，请重试")},re=()=>{o.imageUrl="",r.success("图片已删除")},ie=a=>{const e=a.type==="image/jpeg"||a.type==="image/png",s=a.size/1024/1024<2;return e?s?!0:(r.error("上传图片大小不能超过 2MB!"),!1):(r.error("上传图片只能是 JPG/PNG 格式!"),!1)},ue=async()=>{if(V.value)try{await V.value.validate(),T.value=!0;const a={name:o.name,description:o.description,imageUrl:o.imageUrl,sortOrder:o.sortOrder,status:o.status};if(console.log("=== 提交表单数据 ==="),console.log("当前表单状态:",{...o}),console.log("准备提交的数据:",a),console.log("是否为编辑模式:",E.value),console.log("分类ID:",o.id),E.value){const e=await k.updateCategory(o.id,a);console.log("=== 更新API响应 ==="),console.log("更新结果:",e),e.data&&console.log("返回的imageUrl:",e.data.imageUrl),r.success("更新成功")}else{const e=await k.createCategory(a);console.log("=== 创建API响应 ==="),console.log("创建结果:",e),r.success("创建成功")}x.value=!1,await g(),console.log("=== 刷新后的分类列表 ==="),console.log("更新后的分类数据:",U.value)}catch(a){console.error("提交失败:",a),r.error("提交失败")}finally{T.value=!1}},de=async a=>{try{await O.confirm(`确定要删除分类 "${a.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await k.deleteCategory(a.id),r.success("删除成功"),g()}catch(e){e!=="cancel"&&(console.error("删除分类失败:",e),r.error("删除分类失败"))}},ce=async a=>{try{const e=a.status===1?"禁用":"启用";await O.confirm(`确定要${e}分类"${a.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await k.updateCategoryStatus(a.id,a.status===1?0:1),r.success(`${e}成功`),g()}catch(e){e!=="cancel"&&(console.error("切换分类状态失败:",e),r.error("操作失败"))}},me=a=>{p.value=a},G=async a=>{if(p.value.length===0){r.warning("请先选择要操作的分类");return}const e=p.value.filter(s=>s.status!==a);if(e.length===0){const s=a===1?"启用":"禁用";r.warning(`选中的分类都已经是${s}状态`);return}try{const s=a===1?"启用":"禁用";await O.confirm(`确定要${s}选中的 ${e.length} 个分类吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const c=e.map(h=>k.updateCategoryStatus(h.id,a));await Promise.all(c),r.success(`批量${s}成功，共操作 ${e.length} 个分类`),p.value=[],g()}catch(s){s!=="cancel"&&(console.error("批量操作失败:",s),r.error("批量操作失败"))}},pe=async()=>{if(p.value.length===0){r.warning("请先选择要删除的分类");return}try{await O.confirm(`确定要删除选中的 ${p.value.length} 个分类吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=p.value.map(e=>k.deleteCategory(e.id));await Promise.all(a),r.success("批量删除成功"),p.value=[],g()}catch(a){a!=="cancel"&&(console.error("批量删除失败:",a),r.error("批量删除失败"))}};return Ve(async()=>{console.log("分类管理页面已挂载，开始加载数据...");try{await g(),console.log("分类数据加载完成")}catch(a){console.error("分类数据加载失败:",a),r.error("分类数据加载失败: "+a.message)}}),(a,e)=>{const s=$e,c=ze,h=Re,z=Te,y=Ae,ge=De,_e=Se,f=Ge,fe=je,ve=Le,be=Be,ye=Oe,S=Ke,Ce=He,he=Qe,j=We,we=Ze,ke=Je,Ue=Ie;return v(),$("div",Ye,[i("div",et,[e[13]||(e[13]=i("h2",{class:"page-title"},"分类管理",-1)),t(c,{type:"primary",onClick:le},{default:l(()=>[t(s,null,{default:l(()=>[t(d(J))]),_:1}),e[12]||(e[12]=m(" 添加分类 ",-1))]),_:1,__:[12]})]),i("div",tt,[t(_e,{gutter:16},{default:l(()=>[t(z,{span:6},{default:l(()=>[t(h,{modelValue:b.keyword,"onUpdate:modelValue":e[0]||(e[0]=n=>b.keyword=n),placeholder:"搜索分类名称或描述",clearable:"",onKeyup:Pe(N,["enter"])},{prefix:l(()=>[t(s,null,{default:l(()=>[t(d(K))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(z,{span:4},{default:l(()=>[t(ge,{modelValue:b.status,"onUpdate:modelValue":e[1]||(e[1]=n=>b.status=n),placeholder:"选择状态",clearable:""},{default:l(()=>[t(y,{label:"启用",value:1}),t(y,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),t(z,{span:8},{default:l(()=>[t(c,{type:"primary",onClick:N},{default:l(()=>[t(s,null,{default:l(()=>[t(d(K))]),_:1}),e[14]||(e[14]=m(" 搜索 ",-1))]),_:1,__:[14]}),t(c,{onClick:Y},{default:l(()=>e[15]||(e[15]=[m(" 重置 ",-1)])),_:1,__:[15]}),t(c,{onClick:ee,loading:B.value},{default:l(()=>[t(s,null,{default:l(()=>[t(d(Me))]),_:1}),e[16]||(e[16]=m(" 刷新 ",-1))]),_:1,__:[16]},8,["loading"])]),_:1})]),_:1})]),p.value.length>0?(v(),$("div",at,[i("div",lt,[i("div",st,[i("div",ot,[t(s,null,{default:l(()=>[t(d(Ne))]),_:1}),e[17]||(e[17]=i("span",{class:"summary-label"},"已选择:",-1)),i("span",nt,_(p.value.length)+" 个分类",1)]),i("div",rt,[t(s,null,{default:l(()=>[t(d(Fe))]),_:1}),e[18]||(e[18]=i("span",{class:"summary-label"},"排序范围:",-1)),i("span",it,_(C.value.sortRange),1)]),i("div",ut,[t(s,null,{default:l(()=>[t(d(D))]),_:1}),e[19]||(e[19]=i("span",{class:"summary-label"},"启用:",-1)),i("span",dt,_(C.value.enabledCount),1)]),i("div",ct,[t(s,null,{default:l(()=>[t(d(A))]),_:1}),e[20]||(e[20]=i("span",{class:"summary-label"},"禁用:",-1)),i("span",mt,_(C.value.disabledCount),1)])])]),i("div",pt,[t(c,{type:"success",size:"small",onClick:e[2]||(e[2]=n=>G(1)),disabled:C.value.disabledCount===0},{default:l(()=>[t(s,null,{default:l(()=>[t(d(D))]),_:1}),m(" 批量启用 ("+_(C.value.disabledCount)+") ",1)]),_:1},8,["disabled"]),t(c,{type:"warning",size:"small",onClick:e[3]||(e[3]=n=>G(0)),disabled:C.value.enabledCount===0},{default:l(()=>[t(s,null,{default:l(()=>[t(d(A))]),_:1}),m(" 批量禁用 ("+_(C.value.enabledCount)+") ",1)]),_:1},8,["disabled"]),t(c,{type:"danger",size:"small",onClick:pe},{default:l(()=>[t(s,null,{default:l(()=>[t(d(M))]),_:1}),e[21]||(e[21]=m(" 批量删除 ",-1))]),_:1,__:[21]})])])):q("",!0),t(be,{data:U.value,loading:B.value,stripe:"",style:{width:"100%"},onSelectionChange:me},{default:l(()=>[t(f,{type:"selection",width:"55",align:"center"}),t(f,{label:"序号",width:"80",align:"center"},{default:l(({$index:n})=>[i("span",gt,_((u.current-1)*u.size+n+1),1)]),_:1}),t(f,{label:"分类图片",width:"120"},{default:l(({row:n})=>[n.imageUrl?(v(),I(fe,{key:0,src:n.imageUrl,"preview-src-list":[n.imageUrl],style:{width:"60px",height:"60px","border-radius":"8px"},fit:"cover","preview-teleported":!0},{error:l(()=>[i("div",_t,[t(s,null,{default:l(()=>[t(d(H))]),_:1})])]),_:2},1032,["src","preview-src-list"])):(v(),$("div",ft,[t(s,null,{default:l(()=>[t(d(H))]),_:1}),e[22]||(e[22]=i("span",null,"无图片",-1))]))]),_:1}),t(f,{prop:"name",label:"分类名称"}),t(f,{prop:"description",label:"描述","min-width":"200"}),t(f,{prop:"sortOrder",label:"排序",width:"80"}),t(f,{prop:"status",label:"状态",width:"100"},{default:l(({row:n})=>[t(ve,{type:n.status===1?"success":"danger"},{default:l(()=>[m(_(n.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"createdAt",label:"创建时间",width:"180"},{default:l(({row:n})=>[m(_(d(Xe)(n.createdAt)),1)]),_:1}),t(f,{label:"操作",width:"240",fixed:"right",align:"center"},{default:l(({row:n})=>[i("div",vt,[t(c,{type:"primary",size:"small",onClick:L=>se(n)},{default:l(()=>[t(s,null,{default:l(()=>[t(d(qe))]),_:1}),e[23]||(e[23]=m(" 编辑 ",-1))]),_:2,__:[23]},1032,["onClick"]),t(c,{type:n.status===1?"warning":"success",size:"small",onClick:L=>ce(n)},{default:l(()=>[n.status===1?(v(),I(s,{key:0},{default:l(()=>[t(d(A))]),_:1})):(v(),I(s,{key:1},{default:l(()=>[t(d(D))]),_:1})),m(" "+_(n.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(c,{type:"danger",size:"small",onClick:L=>de(n)},{default:l(()=>[t(s,null,{default:l(()=>[t(d(M))]),_:1}),e[24]||(e[24]=m(" 删除 ",-1))]),_:2,__:[24]},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),i("div",bt,[t(ye,{"current-page":u.current,"onUpdate:currentPage":e[4]||(e[4]=n=>u.current=n),"page-size":u.size,"onUpdate:pageSize":e[5]||(e[5]=n=>u.size=n),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:te,onCurrentChange:ae},null,8,["current-page","page-size","total"])]),t(Ue,{modelValue:x.value,"onUpdate:modelValue":e[11]||(e[11]=n=>x.value=n),title:E.value?"编辑分类":"添加分类",width:"500px"},{footer:l(()=>[t(c,{onClick:e[10]||(e[10]=n=>x.value=!1)},{default:l(()=>e[28]||(e[28]=[m("取消",-1)])),_:1,__:[28]}),t(c,{type:"primary",loading:T.value,onClick:ue},{default:l(()=>[m(_(E.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:l(()=>[t(ke,{ref_key:"formRef",ref:V,model:o,rules:Z,"label-width":"80px"},{default:l(()=>[t(S,{label:"分类名称",prop:"name"},{default:l(()=>[t(h,{modelValue:o.name,"onUpdate:modelValue":e[6]||(e[6]=n=>o.name=n),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),t(S,{label:"描述",prop:"description"},{default:l(()=>[t(h,{modelValue:o.description,"onUpdate:modelValue":e[7]||(e[7]=n=>o.description=n),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])]),_:1}),t(S,{label:"分类图片"},{default:l(()=>[i("div",yt,[t(Ce,{class:"image-uploader",action:W.value,headers:X.value,"show-file-list":!1,"on-success":oe,"on-error":ne,"before-upload":ie},{default:l(()=>[o.imageUrl?(v(),$("img",{key:0,src:o.imageUrl,class:"image"},null,8,Ct)):(v(),I(s,{key:1,class:"image-uploader-icon"},{default:l(()=>[t(d(J))]),_:1}))]),_:1},8,["action","headers"]),o.imageUrl?(v(),$("div",ht,[t(c,{size:"small",type:"danger",plain:"",onClick:re},{default:l(()=>[t(s,null,{default:l(()=>[t(d(M))]),_:1}),e[25]||(e[25]=m(" 删除图片 ",-1))]),_:1,__:[25]})])):q("",!0)])]),_:1}),t(S,{label:"排序",prop:"sortOrder"},{default:l(()=>[t(he,{modelValue:o.sortOrder,"onUpdate:modelValue":e[8]||(e[8]=n=>o.sortOrder=n),min:0,max:999,placeholder:"排序值"},null,8,["modelValue"])]),_:1}),t(S,{label:"状态",prop:"status"},{default:l(()=>[t(we,{modelValue:o.status,"onUpdate:modelValue":e[9]||(e[9]=n=>o.status=n)},{default:l(()=>[t(j,{label:1},{default:l(()=>e[26]||(e[26]=[m("启用",-1)])),_:1,__:[26]}),t(j,{label:0},{default:l(()=>e[27]||(e[27]=[m("禁用",-1)])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ft=xe(wt,[["__scopeId","data-v-f117f7cc"]]);export{Ft as default};
