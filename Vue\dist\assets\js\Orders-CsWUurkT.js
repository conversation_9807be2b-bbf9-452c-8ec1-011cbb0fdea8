import{_ as Ee}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                  *//* empty css               *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                   *//* empty css               *//* empty css                         */import{r as R,a as se,y as Pe,o as Ye,c as D,b as a,G as h,d as t,w as l,aC as Oe,l as Re,t as c,s as Ue,aD as Be,aJ as Ie,f as P,g as _,ar as U,h as He,i as k,aE as Se,k as Ke,e as Y,m,N as ae,aK as Le,aL as je,q as d,aT as qe,a7 as Ge,aU as Je,aa as ne,ab as We,ac as Qe,aV as Xe,ak as Ze,aW as B,b4 as et,al as oe,a9 as tt,ba as lt,b6 as st,bb as j,b7 as q,aH as at,T as nt,a_ as A,B as z,a8 as ot,P as rt,bc as it,aY as dt,bd as ut}from"./index-D5n6Uv5f.js";import{f as G,g as mt,a as I,b as O,e as pt}from"./index-DyZSWDSU.js";const ct={class:"page-container"},ft={class:"search-bar mb-16"},_t={key:0,class:"batch-actions"},vt={class:"batch-info"},yt={class:"batch-summary"},gt={class:"summary-item"},kt={class:"summary-value"},bt={class:"summary-item"},ht={class:"summary-value"},Ct={class:"summary-item"},wt={class:"summary-value"},xt={class:"summary-item"},Dt={class:"summary-value"},Tt={class:"summary-item"},At={class:"summary-value"},zt={class:"summary-item"},Nt={class:"summary-value"},Vt={class:"summary-item"},$t={class:"summary-value"},Ft={class:"summary-item"},Mt={class:"summary-value"},Et={class:"batch-buttons"},Pt={class:"row-number"},Yt={class:"header-with-filter"},Ot={class:"filter-content"},Rt={class:"filter-buttons"},Ut={class:"order-no"},Bt={class:"header-with-filter"},It={class:"filter-content"},Ht={class:"filter-buttons"},St={class:"user-info"},Kt={class:"user-name"},Lt={class:"header-with-filter"},jt={class:"filter-content"},qt={class:"filter-buttons"},Gt={class:"header-with-filter"},Jt={class:"filter-content"},Wt={class:"filter-buttons"},Qt={class:"header-with-filter"},Xt={class:"header-with-filter"},Zt={class:"filter-content"},el={class:"amount-filter"},tl={class:"filter-buttons"},ll={class:"amount"},sl={class:"header-with-filter"},al={class:"header-with-filter"},nl={class:"filter-content"},ol={class:"date-filter"},rl={class:"filter-buttons"},il={class:"header-with-filter"},dl={class:"time-info"},ul={key:0,class:"time-item paid"},ml={class:"time-text"},pl={key:1,class:"time-item shipped"},cl={class:"time-text"},fl={key:2,class:"time-item delivered"},_l={class:"time-text"},vl={key:3,class:"time-item expected"},yl={class:"time-text"},gl={key:4,class:"time-item expected"},kl={class:"time-text"},bl={key:5,class:"time-item empty"},hl={class:"header-with-filter"},Cl={class:"remark-info"},wl={key:0,class:"remark-item order-remark"},xl={class:"remark-content"},Dl={key:1,class:"remark-item delivery-remark"},Tl={class:"remark-content delivery-note"},Al={key:2,class:"no-remark"},zl={class:"action-buttons"},Nl={class:"pagination-container"},Vl={__name:"Orders",setup($l){const re=He(),H=R(!1),N=R([]),y=R([]),J=s=>{if(!s)return"";if(s.includes("2024")||s.includes("2025"))return s;const e=new Date().getFullYear();return s.includes("月")&&s.includes("日")?`${e}年 ${s}`:s},ie=s=>s.paidAt||s.shippedAt||s.deliveredAt||s.deliveryTime||s.pickupTime,de=s=>{s===""?o.status=null:o.status=parseInt(s),p.current=1,x()},ue=s=>{s===""?o.deliveryType=null:o.deliveryType=parseInt(s),p.current=1,b()},W=()=>{p.current=1,b()},me=()=>{o.orderNo="",p.current=1,b()},Q=()=>{p.current=1,x()},pe=()=>{o.keyword="",p.current=1,x()},X=()=>{p.current=1,b()},ce=()=>{o.recipientName="",p.current=1,b()},Z=()=>{p.current=1,b()},fe=()=>{o.recipientPhone="",p.current=1,b()},_e=()=>{p.current=1,b()},ve=()=>{o.minAmount=null,o.maxAmount=null,p.current=1,b()},ye=()=>{p.current=1,b()},ge=()=>{o.startDate="",o.endDate="",p.current=1,b()},ke=s=>{switch(s){case"paid":o.status=2;break;case"shipped":o.status=3;break;case"delivered":o.status=4;break;case"pending":o.status=1;break;default:o.status=null}p.current=1,x()},be=s=>{s==="hasRemark"?o.hasRemark=!0:s==="noRemark"?o.hasRemark=!1:o.hasRemark=null,p.current=1,b()},o=se({keyword:"",status:null,deliveryType:null,recipientName:"",recipientPhone:"",orderNo:"",minAmount:null,maxAmount:null,startDate:"",endDate:"",hasRemark:null}),p=se({current:1,size:10,total:0}),C=Pe(()=>{if(y.value.length===0)return{totalAmount:"0.00",userCount:0,statusCounts:{pending:0,paid:0,shipping:0,completed:0,cancelled:0}};const s=y.value.reduce((i,g)=>i+(parseFloat(g.totalAmount)||0),0),r=new Set(y.value.map(i=>i.userId)).size,u={pending:y.value.filter(i=>i.status===1).length,paid:y.value.filter(i=>i.status===2).length,shipping:y.value.filter(i=>i.status===3).length,completed:y.value.filter(i=>i.status===4).length,cancelled:y.value.filter(i=>i.status===5).length};return{totalAmount:s.toFixed(2),userCount:r,statusCounts:u}}),V=R([]),x=async()=>{H.value=!0;try{const s={current:1,size:1e3,keyword:o.keyword,status:o.status};Object.keys(s).forEach(r=>{(s[r]===null||s[r]===void 0||s[r]==="")&&delete s[r]}),console.log("请求参数:",s);const e=await P.getOrders(s);e&&e.data?(V.value=e.data.records||[],console.log("从后端获取订单数据:",V.value.length,"条")):V.value=[],b()}catch(s){console.error("加载订单列表失败:",s),_.error("加载订单列表失败"),V.value=[],N.value=[],p.total=0}finally{H.value=!1}},ee=()=>{p.current=1,x()},he=()=>{o.keyword="",o.status=null,o.deliveryType=null,o.recipientName="",o.recipientPhone="",o.orderNo="",o.minAmount=null,o.maxAmount=null,o.startDate="",o.endDate="",o.hasRemark=null,p.current=1,x(),_.success("已重置所有筛选条件")},Ce=()=>{x(),_({message:"订单数据已刷新",type:"success",duration:1500,showClose:!1})},we=s=>{console.log("查看详情，订单ID:",s.id);try{re.push(`/orders/${s.id}`)}catch(e){console.error("跳转详情页失败:",e),_.error("跳转详情页失败")}},b=()=>{let s=[...V.value];o.orderNo&&(s=s.filter(u=>u.orderNo&&u.orderNo.toLowerCase().includes(o.orderNo.toLowerCase())||u.id.toString().includes(o.orderNo))),o.deliveryType!==null&&(s=s.filter(u=>u.deliveryType===o.deliveryType)),o.recipientName&&(s=s.filter(u=>u.recipientName&&u.recipientName.includes(o.recipientName))),o.recipientPhone&&(s=s.filter(u=>u.recipientPhone&&u.recipientPhone.includes(o.recipientPhone))),(o.minAmount!==null||o.maxAmount!==null)&&(s=s.filter(u=>{const i=parseFloat(u.totalAmount),g=o.minAmount?parseFloat(o.minAmount):0,T=o.maxAmount?parseFloat(o.maxAmount):1/0;return i>=g&&i<=T})),(o.startDate||o.endDate)&&(s=s.filter(u=>{const i=new Date(u.createdAt).toISOString().split("T")[0],g=o.startDate||"1900-01-01",T=o.endDate||"2099-12-31";return i>=g&&i<=T})),o.hasRemark!==null&&(s=s.filter(u=>{const i=!!(u.remark||u.deliveryNotes);return o.hasRemark?i:!i})),p.total=s.length;const e=(p.current-1)*p.size,r=e+p.size;N.value=s.slice(e,r),console.log("筛选结果:",{原始数据:V.value.length,筛选后:s.length,当前页:N.value.length,总页数:Math.ceil(p.total/p.size)})},xe=s=>{p.size=s,p.current=1,b()},De=s=>{p.current=s,b()},Te=async(s,e)=>{const r=I(e),u=s.deliveryType===1?"外卖配送":"到店自取";let i=`
    <div style="text-align: left; line-height: 1.6;">
      <p><strong>订单信息：</strong></p>
      <p>• 订单号：${s.id}</p>
      <p>• 用户：${s.userName||"未知用户"}</p>
      <p>• 收货人：${s.recipientName||"未设置"}</p>
      <p>• 配送方式：${u}</p>
      <p>• 订单金额：¥${G(s.totalAmount)}</p>
      <p>• 当前状态：${I(s.status)}</p>
      <br>
      <p><strong style="color: #e6a23c;">即将更新为：${r}</strong></p>
    </div>
  `;e===5?i+=`
      <div style="color: #f56c6c; margin-top: 10px; padding: 10px; background: #fef0f0; border-radius: 4px;">
        <strong>⚠️ 注意：</strong>取消订单后将无法恢复，请确认操作！
      </div>
    `:e===3?i+=`
      <div style="color: #409eff; margin-top: 10px; padding: 10px; background: #ecf5ff; border-radius: 4px;">
        <strong>🚚 提醒：</strong>确认商品已${s.deliveryType===1?"开始配送":"准备好自取"}？
      </div>
    `:e===1?i+=`
      <div style="color: #e6a23c; margin-top: 10px; padding: 10px; background: #fdf6ec; border-radius: 4px;">
        <strong>📦 提醒：</strong>确认商品已送达客户？
      </div>
    `:e===4&&(i+=`
      <div style="color: #67c23a; margin-top: 10px; padding: 10px; background: #f0f9ff; border-radius: 4px;">
        <strong>💰 提醒：</strong>确认客户已完成付款？
      </div>
    `);try{if(await U.confirm(i,"订单状态更新确认",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,customClass:"order-status-confirm-dialog"}),e===5||e===1||e===4){let g="",T="warning";e===5?(g="最后确认：真的要取消这个订单吗？",T="error"):e===1?(g="最后确认：商品真的已经送达客户了吗？",T="warning"):e===4&&(g="最后确认：客户真的已经完成付款了吗？",T="success"),await U.confirm(g,"二次确认",{confirmButtonText:"确定执行",cancelButtonText:"我再想想",type:T,center:!0})}await P.updateOrderStatus(s.id,e),_.success({message:`订单状态已成功更新为"${r}"`,duration:3e3}),x()}catch(g){g!=="cancel"&&(console.error("更新订单状态失败:",g),_.error("更新订单状态失败"))}},Ae=async(s,e)=>{e==="4"?await ze(s):await Te(s,parseInt(e))},ze=async s=>{try{await U.confirm(`确定要确认订单 ${s.id} 的客户已完成付款吗？确认后用户将可以确认收货。`,"确认付款",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await P.confirmPayment(s.id),_.success("付款确认成功，等待用户确认收货"),x()}catch(e){e!=="cancel"&&(console.error("确认付款失败:",e),_.error("确认付款失败"))}},Ne=s=>{y.value=s},S=async s=>{if(y.value.length===0){_.warning("请先选择要操作的订单");return}let e=[],r="";switch(s){case 3:e=y.value.filter(u=>u.status===2),r="发货";break;case 4:e=y.value.filter(u=>u.status===3),r="完成";break;case 5:e=y.value.filter(u=>u.status===1||u.status===2),r="取消";break;default:_.error("不支持的操作");return}if(e.length===0){_.warning(`没有可${r}的订单`);return}try{await U.confirm(`确定要${r}选中的 ${e.length} 个订单吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const u=e.map(i=>P.updateOrderStatus(i.id,s));await Promise.all(u),_.success(`批量${r}成功，共操作 ${e.length} 个订单`),y.value=[],x()}catch(u){u!=="cancel"&&(console.error("批量操作失败:",u),_.error("批量操作失败"))}},K=(s,e)=>{pt(s,["订单号","用户昵称","用户电话","订单状态","配送方式","订单金额","收货人","收货电话","收货地址","备注","创建时间"],e,i=>[i.orderNo||"",i.userNickname||"",i.userPhone||"",I(i.status),i.deliveryType===1?"配送":"自提",`¥${G(i.totalAmount)}`,i.recipientName||"",i.recipientPhone||"",`${i.recipientProvince||""}${i.recipientCity||""}${i.recipientDistrict||""}${i.recipientAddress||""}`,i.remark||"",O(i.createTime)])},Ve=async s=>{try{switch(s){case"current":if(N.value.length===0){_.warning("当前页面没有订单数据");return}K(N.value,`订单数据_第${p.current}页_${new Date().toLocaleDateString()}.csv`),_.success(`已导出当前页 ${N.value.length} 条订单数据`);break;case"all":_.info("正在导出全部数据，请稍候...");try{const r=(await P.getOrders({current:1,size:1e4,keyword:o.keyword,status:o.status})).data.records||[];if(r.length===0){_.warning("没有订单数据可导出");return}K(r,`全部订单数据_${new Date().toLocaleDateString()}.csv`),_.success(`全部订单数据导出完成，共 ${r.length} 条`)}catch(e){console.error("导出全部数据失败:",e),_.error("导出全部数据失败")}break;case"selected":if(y.value.length===0){_.warning("请先选择要导出的订单");return}K(y.value,`选中订单数据_${y.value.length}条_${new Date().toLocaleDateString()}.csv`),_.success(`已导出选中的 ${y.value.length} 条订单数据`);break}}catch(e){console.error("导出失败:",e),_.error("导出失败: "+e.message)}};return Ye(()=>{x()}),(s,e)=>{const r=Re,u=Ke,i=Se,g=je,T=Le,v=Ue,f=Qe,$=We,F=Ge,$e=Oe,w=at,M=nt,te=ot,L=rt,le=it,Fe=Be,Me=Ie;return k(),D("div",ct,[e[79]||(e[79]=a("div",{class:"page-header"},[a("h2",{class:"page-title"},"订单管理")],-1)),a("div",ft,[t($e,{gutter:16},{default:l(()=>[t(i,{span:6},{default:l(()=>[t(u,{modelValue:o.keyword,"onUpdate:modelValue":e[0]||(e[0]=n=>o.keyword=n),placeholder:"搜索订单号或用户",clearable:"",onKeyup:Y(ee,["enter"])},{prefix:l(()=>[t(r,null,{default:l(()=>[t(m(ae))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(i,{span:4},{default:l(()=>[t(T,{modelValue:o.status,"onUpdate:modelValue":e[1]||(e[1]=n=>o.status=n),placeholder:"选择状态",clearable:""},{default:l(()=>[t(g,{label:"待支付",value:1}),t(g,{label:"已支付",value:2}),t(g,{label:"配送中",value:3}),t(g,{label:"已完成",value:4}),t(g,{label:"已取消",value:5})]),_:1},8,["modelValue"])]),_:1}),t(i,{span:8},{default:l(()=>[t(v,{type:"primary",onClick:ee},{default:l(()=>[t(r,null,{default:l(()=>[t(m(ae))]),_:1}),e[15]||(e[15]=d(" 搜索 ",-1))]),_:1,__:[15]}),t(v,{onClick:he},{default:l(()=>e[16]||(e[16]=[d(" 重置筛选 ",-1)])),_:1,__:[16]}),t(v,{onClick:Ce},{default:l(()=>[t(r,null,{default:l(()=>[t(m(qe))]),_:1}),e[17]||(e[17]=d(" 刷新 ",-1))]),_:1,__:[17]}),t(F,{onCommand:Ve,class:"export-dropdown"},{dropdown:l(()=>[t($,null,{default:l(()=>[t(f,{command:"current"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(Xe))]),_:1}),e[19]||(e[19]=d(" 导出当前页 ",-1))]),_:1,__:[19]}),t(f,{command:"all"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(Ze))]),_:1}),e[20]||(e[20]=d(" 导出全部数据 ",-1))]),_:1,__:[20]}),t(f,{command:"selected",divided:""},{default:l(()=>[t(r,null,{default:l(()=>[t(m(B))]),_:1}),e[21]||(e[21]=d(" 导出已选择 ",-1))]),_:1,__:[21]})]),_:1})]),default:l(()=>[t(v,{size:"default",class:"export-btn"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(Je))]),_:1}),e[18]||(e[18]=a("span",null,"导出",-1)),t(r,{class:"dropdown-icon"},{default:l(()=>[t(m(ne))]),_:1})]),_:1,__:[18]})]),_:1})]),_:1})]),_:1})]),y.value.length>0?(k(),D("div",_t,[a("div",vt,[a("div",yt,[a("div",gt,[t(r,null,{default:l(()=>[t(m(et))]),_:1}),e[22]||(e[22]=a("span",{class:"summary-label"},"已选择:",-1)),a("span",kt,c(y.value.length)+" 个订单",1)]),a("div",bt,[t(r,null,{default:l(()=>[t(m(oe))]),_:1}),e[23]||(e[23]=a("span",{class:"summary-label"},"总金额:",-1)),a("span",ht,"¥"+c(C.value.totalAmount),1)]),a("div",Ct,[t(r,null,{default:l(()=>[t(m(tt))]),_:1}),e[24]||(e[24]=a("span",{class:"summary-label"},"用户数:",-1)),a("span",wt,c(C.value.userCount)+" 个",1)]),a("div",xt,[t(r,null,{default:l(()=>[t(m(lt))]),_:1}),e[25]||(e[25]=a("span",{class:"summary-label"},"待支付:",-1)),a("span",Dt,c(C.value.statusCounts.pending),1)]),a("div",Tt,[t(r,null,{default:l(()=>[t(m(st))]),_:1}),e[26]||(e[26]=a("span",{class:"summary-label"},"已支付:",-1)),a("span",At,c(C.value.statusCounts.paid),1)]),a("div",zt,[t(r,null,{default:l(()=>[t(m(j))]),_:1}),e[27]||(e[27]=a("span",{class:"summary-label"},"配送中:",-1)),a("span",Nt,c(C.value.statusCounts.shipping),1)]),a("div",Vt,[t(r,null,{default:l(()=>[t(m(B))]),_:1}),e[28]||(e[28]=a("span",{class:"summary-label"},"已完成:",-1)),a("span",$t,c(C.value.statusCounts.completed),1)]),a("div",Ft,[t(r,null,{default:l(()=>[t(m(q))]),_:1}),e[29]||(e[29]=a("span",{class:"summary-label"},"已取消:",-1)),a("span",Mt,c(C.value.statusCounts.cancelled),1)])])]),a("div",Et,[t(v,{type:"success",size:"small",onClick:e[2]||(e[2]=n=>S(3)),disabled:C.value.statusCounts.paid===0},{default:l(()=>[t(r,null,{default:l(()=>[t(m(j))]),_:1}),d(" 批量发货 ("+c(C.value.statusCounts.paid)+") ",1)]),_:1},8,["disabled"]),t(v,{type:"primary",size:"small",onClick:e[3]||(e[3]=n=>S(4)),disabled:C.value.statusCounts.shipping===0},{default:l(()=>[t(r,null,{default:l(()=>[t(m(B))]),_:1}),d(" 批量完成 ("+c(C.value.statusCounts.shipping)+") ",1)]),_:1},8,["disabled"]),t(v,{type:"danger",size:"small",onClick:e[4]||(e[4]=n=>S(5)),disabled:C.value.statusCounts.pending===0&&C.value.statusCounts.paid===0},{default:l(()=>[t(r,null,{default:l(()=>[t(m(q))]),_:1}),e[30]||(e[30]=d(" 批量取消 ",-1))]),_:1,__:[30]},8,["disabled"])])])):h("",!0),t(Fe,{data:N.value,loading:H.value,stripe:"",style:{width:"100%","min-width":"1100px"},onSelectionChange:Ne,size:"small"},{default:l(()=>[t(w,{type:"selection",width:"50",align:"center"}),t(w,{label:"序号",width:"70",align:"center"},{default:l(({$index:n})=>[a("span",Pt,c((p.current-1)*p.size+n+1),1)]),_:1}),t(w,{label:"订单号","min-width":"140","show-overflow-tooltip":""},{header:l(()=>[a("div",Yt,[e[33]||(e[33]=a("span",null,"订单号",-1)),t(M,{placement:"bottom",width:200,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",Ot,[t(u,{modelValue:o.orderNo,"onUpdate:modelValue":e[5]||(e[5]=n=>o.orderNo=n),placeholder:"输入订单号",size:"small",clearable:"",onKeyup:Y(W,["enter"])},null,8,["modelValue"]),a("div",Rt,[t(v,{size:"small",onClick:me},{default:l(()=>e[31]||(e[31]=[d("重置",-1)])),_:1,__:[31]}),t(v,{size:"small",type:"primary",onClick:W},{default:l(()=>e[32]||(e[32]=[d("确定",-1)])),_:1,__:[32]})])])]),_:1})])]),default:l(({row:n})=>[a("span",Ut,c(n.orderNo||n.id),1)]),_:1}),t(w,{prop:"userName",label:"用户","min-width":"140","show-overflow-tooltip":""},{header:l(()=>[a("div",Bt,[e[36]||(e[36]=a("span",null,"用户",-1)),t(M,{placement:"bottom",width:200,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",It,[t(u,{modelValue:o.keyword,"onUpdate:modelValue":e[6]||(e[6]=n=>o.keyword=n),placeholder:"输入用户名",size:"small",clearable:"",onKeyup:Y(Q,["enter"])},null,8,["modelValue"]),a("div",Ht,[t(v,{size:"small",onClick:pe},{default:l(()=>e[34]||(e[34]=[d("重置",-1)])),_:1,__:[34]}),t(v,{size:"small",type:"primary",onClick:Q},{default:l(()=>e[35]||(e[35]=[d("确定",-1)])),_:1,__:[35]})])])]),_:1})])]),default:l(({row:n})=>[a("div",St,[n.userAvatar?(k(),z(te,{key:0,src:n.userAvatar,size:28,fit:"cover",class:"user-avatar"},null,8,["src"])):(k(),z(te,{key:1,size:28,class:"user-avatar"},{default:l(()=>{var E;return[d(c(((E=n.userName)==null?void 0:E.charAt(0))||"U"),1)]}),_:2},1024)),a("span",Kt,c(n.userName||"未知用户"),1)])]),_:1}),t(w,{prop:"recipientName",label:"收货人","min-width":"100","show-overflow-tooltip":""},{header:l(()=>[a("div",Lt,[e[39]||(e[39]=a("span",null,"收货人",-1)),t(M,{placement:"bottom",width:200,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",jt,[t(u,{modelValue:o.recipientName,"onUpdate:modelValue":e[7]||(e[7]=n=>o.recipientName=n),placeholder:"输入收货人姓名",size:"small",clearable:"",onKeyup:Y(X,["enter"])},null,8,["modelValue"]),a("div",qt,[t(v,{size:"small",onClick:ce},{default:l(()=>e[37]||(e[37]=[d("重置",-1)])),_:1,__:[37]}),t(v,{size:"small",type:"primary",onClick:X},{default:l(()=>e[38]||(e[38]=[d("确定",-1)])),_:1,__:[38]})])])]),_:1})])]),_:1}),t(w,{prop:"recipientPhone",label:"联系电话","min-width":"120","show-overflow-tooltip":""},{header:l(()=>[a("div",Gt,[e[42]||(e[42]=a("span",null,"联系电话",-1)),t(M,{placement:"bottom",width:200,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",Jt,[t(u,{modelValue:o.recipientPhone,"onUpdate:modelValue":e[8]||(e[8]=n=>o.recipientPhone=n),placeholder:"输入手机号",size:"small",clearable:"",onKeyup:Y(Z,["enter"])},null,8,["modelValue"]),a("div",Wt,[t(v,{size:"small",onClick:fe},{default:l(()=>e[40]||(e[40]=[d("重置",-1)])),_:1,__:[40]}),t(v,{size:"small",type:"primary",onClick:Z},{default:l(()=>e[41]||(e[41]=[d("确定",-1)])),_:1,__:[41]})])])]),_:1})])]),_:1}),t(w,{prop:"deliveryType",label:"配送方式","min-width":"100",align:"center"},{header:l(()=>[a("div",Qt,[e[46]||(e[46]=a("span",null,"配送方式",-1)),t(F,{trigger:"click",onCommand:ue},{dropdown:l(()=>[t($,null,{default:l(()=>[t(f,{command:""},{default:l(()=>e[43]||(e[43]=[d("全部方式",-1)])),_:1,__:[43]}),t(f,{command:"1"},{default:l(()=>e[44]||(e[44]=[d("外卖配送",-1)])),_:1,__:[44]}),t(f,{command:"2"},{default:l(()=>e[45]||(e[45]=[d("到店自取",-1)])),_:1,__:[45]})]),_:1})]),default:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),_:1})])]),default:l(({row:n})=>[t(L,{type:n.deliveryType===1?"primary":"success",size:"small"},{default:l(()=>[d(c(n.deliveryType===1?"外卖配送":"到店自取"),1)]),_:2},1032,["type"])]),_:1}),t(w,{prop:"totalAmount",label:"订单金额","min-width":"100",align:"right"},{header:l(()=>[a("div",Xt,[e[50]||(e[50]=a("span",null,"订单金额",-1)),t(M,{placement:"bottom",width:250,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",Zt,[a("div",el,[t(u,{modelValue:o.minAmount,"onUpdate:modelValue":e[9]||(e[9]=n=>o.minAmount=n),placeholder:"最小金额",size:"small",type:"number",clearable:""},null,8,["modelValue"]),e[47]||(e[47]=a("span",{class:"amount-separator"},"-",-1)),t(u,{modelValue:o.maxAmount,"onUpdate:modelValue":e[10]||(e[10]=n=>o.maxAmount=n),placeholder:"最大金额",size:"small",type:"number",clearable:""},null,8,["modelValue"])]),a("div",tl,[t(v,{size:"small",onClick:ve},{default:l(()=>e[48]||(e[48]=[d("重置",-1)])),_:1,__:[48]}),t(v,{size:"small",type:"primary",onClick:_e},{default:l(()=>e[49]||(e[49]=[d("确定",-1)])),_:1,__:[49]})])])]),_:1})])]),default:l(({row:n})=>[a("span",ll,"¥"+c(m(G)(n.totalAmount)),1)]),_:1}),t(w,{prop:"status",label:"当前状态","min-width":"100",align:"center"},{header:l(()=>[a("div",sl,[e[57]||(e[57]=a("span",null,"当前状态",-1)),t(F,{trigger:"click",onCommand:de},{dropdown:l(()=>[t($,null,{default:l(()=>[t(f,{command:""},{default:l(()=>e[51]||(e[51]=[d("全部状态",-1)])),_:1,__:[51]}),t(f,{command:"1"},{default:l(()=>e[52]||(e[52]=[d("待付款",-1)])),_:1,__:[52]}),t(f,{command:"2"},{default:l(()=>e[53]||(e[53]=[d("已付款",-1)])),_:1,__:[53]}),t(f,{command:"3"},{default:l(()=>e[54]||(e[54]=[d("已发货",-1)])),_:1,__:[54]}),t(f,{command:"4"},{default:l(()=>e[55]||(e[55]=[d("已完成",-1)])),_:1,__:[55]}),t(f,{command:"5"},{default:l(()=>e[56]||(e[56]=[d("已取消",-1)])),_:1,__:[56]})]),_:1})]),default:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),_:1})])]),default:l(({row:n})=>[t(L,{type:m(mt)(n.status),size:"small"},{default:l(()=>[d(c(m(I)(n.status)),1)]),_:2},1032,["type"])]),_:1}),t(w,{prop:"createdAt",label:"下单时间","min-width":"150","show-overflow-tooltip":""},{header:l(()=>[a("div",al,[e[61]||(e[61]=a("span",null,"下单时间",-1)),t(M,{placement:"bottom",width:300,trigger:"click"},{reference:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),default:l(()=>[a("div",nl,[a("div",ol,[t(le,{modelValue:o.startDate,"onUpdate:modelValue":e[11]||(e[11]=n=>o.startDate=n),type:"date",placeholder:"开始日期",size:"small",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"]),e[58]||(e[58]=a("span",{class:"date-separator"},"至",-1)),t(le,{modelValue:o.endDate,"onUpdate:modelValue":e[12]||(e[12]=n=>o.endDate=n),type:"date",placeholder:"结束日期",size:"small",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),a("div",rl,[t(v,{size:"small",onClick:ge},{default:l(()=>e[59]||(e[59]=[d("重置",-1)])),_:1,__:[59]}),t(v,{size:"small",type:"primary",onClick:ye},{default:l(()=>e[60]||(e[60]=[d("确定",-1)])),_:1,__:[60]})])])]),_:1})])]),default:l(({row:n})=>[d(c(m(O)(n.createdAt,"YYYY-MM-DD HH:mm")),1)]),_:1}),t(w,{label:"关键时间","min-width":"180","show-overflow-tooltip":""},{header:l(()=>[a("div",il,[e[67]||(e[67]=a("span",null,"关键时间",-1)),t(F,{trigger:"click",onCommand:ke},{dropdown:l(()=>[t($,null,{default:l(()=>[t(f,{command:""},{default:l(()=>e[62]||(e[62]=[d("全部订单",-1)])),_:1,__:[62]}),t(f,{command:"paid"},{default:l(()=>e[63]||(e[63]=[d("已支付",-1)])),_:1,__:[63]}),t(f,{command:"shipped"},{default:l(()=>e[64]||(e[64]=[d("已发货",-1)])),_:1,__:[64]}),t(f,{command:"delivered"},{default:l(()=>e[65]||(e[65]=[d("已完成",-1)])),_:1,__:[65]}),t(f,{command:"pending"},{default:l(()=>e[66]||(e[66]=[d("待处理",-1)])),_:1,__:[66]})]),_:1})]),default:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),_:1})])]),default:l(({row:n})=>[a("div",dl,[n.paidAt?(k(),D("div",ul,[a("span",ml,"💰 支付 "+c(m(O)(n.paidAt,"MM-DD HH:mm")),1)])):h("",!0),n.shippedAt?(k(),D("div",pl,[a("span",cl,"🚚 "+c(n.deliveryType===1?"配送":"准备")+" "+c(m(O)(n.shippedAt,"MM-DD HH:mm")),1)])):h("",!0),n.deliveredAt?(k(),D("div",fl,[a("span",_l,"✅ 完成 "+c(m(O)(n.deliveredAt,"MM-DD HH:mm")),1)])):h("",!0),n.deliveryType===1&&n.deliveryTime&&!n.deliveredAt?(k(),D("div",vl,[a("span",yl,"⏰ "+c(J(n.deliveryTime)),1)])):h("",!0),n.deliveryType===2&&n.pickupTime&&!n.deliveredAt?(k(),D("div",gl,[a("span",kl,"⏰ "+c(J(n.pickupTime)),1)])):h("",!0),ie(n)?h("",!0):(k(),D("div",bl,e[68]||(e[68]=[a("span",{class:"no-time"},"待处理",-1)])))])]),_:1}),t(w,{label:"备注","min-width":"150"},{header:l(()=>[a("div",hl,[e[72]||(e[72]=a("span",null,"备注",-1)),t(F,{trigger:"click",onCommand:be},{dropdown:l(()=>[t($,null,{default:l(()=>[t(f,{command:""},{default:l(()=>e[69]||(e[69]=[d("全部订单",-1)])),_:1,__:[69]}),t(f,{command:"hasRemark"},{default:l(()=>e[70]||(e[70]=[d("有备注",-1)])),_:1,__:[70]}),t(f,{command:"noRemark"},{default:l(()=>e[71]||(e[71]=[d("无备注",-1)])),_:1,__:[71]})]),_:1})]),default:l(()=>[t(r,{class:"filter-icon"},{default:l(()=>[t(m(A))]),_:1})]),_:1})])]),default:l(({row:n})=>[a("div",Cl,[n.remark?(k(),D("div",wl,[a("div",xl,c(n.remark),1)])):h("",!0),n.deliveryNotes?(k(),D("div",Dl,[a("div",Tl,c(n.deliveryNotes),1)])):h("",!0),!n.remark&&!n.deliveryNotes?(k(),D("div",Al,e[73]||(e[73]=[a("span",null,"-",-1)]))):h("",!0)])]),_:1}),t(w,{label:"操作",width:"180",fixed:"right",align:"center"},{default:l(({row:n})=>[a("div",zl,[t(v,{type:"primary",size:"small",onClick:E=>we(n),class:"action-btn"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(dt))]),_:1}),e[74]||(e[74]=d(" 详情 ",-1))]),_:2,__:[74]},1032,["onClick"]),n.status!==4&&n.status!==5?(k(),z(F,{key:0,onCommand:E=>Ae(n,E),trigger:"click"},{dropdown:l(()=>[t($,null,{default:l(()=>[n.status===2?(k(),z(f,{key:0,command:"3"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(j))]),_:1}),d(" "+c(n.deliveryType===1?"开始配送":"准备自取"),1)]),_:2},1024)):h("",!0),n.status===3?(k(),z(f,{key:1,command:"1"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(oe))]),_:1}),e[76]||(e[76]=d(" 确认送达 ",-1))]),_:1,__:[76]})):h("",!0),n.status===1&&n.paymentStatus===0?(k(),z(f,{key:2,command:"4"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(B))]),_:1}),e[77]||(e[77]=d(" 确认付款 ",-1))]),_:1,__:[77]})):h("",!0),n.status<4?(k(),z(f,{key:3,command:"5",divided:""},{default:l(()=>[t(r,null,{default:l(()=>[t(m(q))]),_:1}),e[78]||(e[78]=d(" 取消订单 ",-1))]),_:1,__:[78]})):h("",!0)]),_:2},1024)]),default:l(()=>[t(v,{type:"success",size:"small",class:"action-btn"},{default:l(()=>[e[75]||(e[75]=d(" 状态操作 ",-1)),t(r,null,{default:l(()=>[t(m(ne))]),_:1})]),_:1,__:[75]})]),_:2},1032,["onCommand"])):(k(),z(L,{key:1,type:n.status===4?"success":"danger",size:"small"},{default:l(()=>[t(r,null,{default:l(()=>[t(m(ut))]),_:1}),d(" "+c(n.status===4?"已完成":"已取消"),1)]),_:2},1032,["type"]))])]),_:1})]),_:1},8,["data","loading"]),a("div",Nl,[t(Me,{"current-page":p.current,"onUpdate:currentPage":e[13]||(e[13]=n=>p.current=n),"page-size":p.size,"onUpdate:pageSize":e[14]||(e[14]=n=>p.size=n),"page-sizes":[10,20,50,100],total:p.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:De},null,8,["current-page","page-size","total"])])])}}},Ll=Ee(Vl,[["__scopeId","data-v-47859bf6"]]);export{Ll as default};
