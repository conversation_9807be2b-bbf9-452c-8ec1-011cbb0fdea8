import{_ as ge}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                        *//* empty css                     */import{r as w,a as z,y as N,o as _e,c as x,b as d,d as t,w as l,s as fe,E as ve,I as be,G as we,b8 as he,B as I,aJ as Ue,a1 as ye,bo as U,g as r,aD as Ve,i as v,q as u,l as Ee,m as y,aO as R,j as ke,k as xe,aK as Ce,aL as Se,N as ze,aT as Ne,aH as Ie,aZ as Be,ap as Oe,a3 as Te,t as Le,aQ as Fe,b0 as Re,aF as De,b3 as $e,ar as D,aA as Me}from"./index-BiIMV38A.js";const je={class:"swiper-management"},qe={class:"header"},Ae={class:"search-area"},We={class:"table-area"},Ge={class:"image-container"},He={class:"image-error"},Pe={key:1,class:"no-image"},Je={key:0,class:"batch-actions"},Ke={class:"pagination"},Qe={class:"upload-area"},Ze=["src"],Xe={class:"dialog-footer"},Ye={__name:"SwiperManagement",setup(ea){const C=w(!1),S=w(!1),b=w(!1),B=w([]),f=w([]),c=z({title:"",status:null}),n=z({pageNum:1,pageSize:10,total:0}),s=z({id:null,title:"",imageUrl:"",linkUrl:"",sortOrder:0,status:1,description:""}),h=w(),$={title:[{required:!0,message:"请输入轮播图标题",trigger:"blur"},{min:1,max:100,message:"标题长度在 1 到 100 个字符",trigger:"blur"}],imageUrl:[{required:!0,message:"请上传轮播图图片",trigger:"change"}],sortOrder:[{required:!0,message:"请输入排序顺序",trigger:"blur"}]},M=N(()=>s.id?"编辑轮播图":"新增轮播图"),j=N(()=>"/api/swiper/upload"),q=N(()=>({Authorization:localStorage.getItem("token")||""})),V=a=>{if(!a)return"";if(a.startsWith("http"))return console.log("Complete URL:",a),a;let e;return a.startsWith("/")?e=`http://localhost:8080/api${a}`:e=`http://localhost:8080/api/${a}`,console.log("Original URL:",a,"Full URL:",e),e},g=async()=>{C.value=!0;try{const a={pageNum:n.pageNum,pageSize:n.pageSize,...c},e=await U.get("/api/swiper/page",{params:a});e.data.code===200?(B.value=e.data.data.records,n.total=e.data.data.total):r.error(e.data.message||"获取数据失败")}catch(a){r.error("网络错误，请稍后重试"),console.error("Error loading data:",a)}finally{C.value=!1}},A=()=>{n.pageNum=1,g()},W=()=>{c.title="",c.status=null,n.pageNum=1,g()},G=a=>{n.pageSize=a,n.pageNum=1,g()},H=a=>{n.pageNum=a,g()},P=a=>{f.value=a},J=()=>{O(),b.value=!0},K=a=>{Object.assign(s,{...a}),b.value=!0},O=()=>{h.value&&h.value.resetFields(),Object.assign(s,{id:null,title:"",imageUrl:"",linkUrl:"",sortOrder:0,status:1,description:""})},Q=async()=>{if(h.value)try{await h.value.validate(),S.value=!0;const a=(s.id,"/api/swiper"),e=s.id?"put":"post",i=await U[e](a,s);i.data.code===200?(r.success(i.data.message||"操作成功"),b.value=!1,g()):r.error(i.data.message||"操作失败")}catch(a){a.name!=="ValidationError"&&(r.error("网络错误，请稍后重试"),console.error("Error submitting form:",a))}finally{S.value=!1}},Z=async a=>{try{await D.confirm(`确定要删除轮播图"${a.title}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await U.delete(`/api/swiper/${a.id}`);e.data.code===200?(r.success("删除成功"),g()):r.error(e.data.message||"删除失败")}catch(e){e!=="cancel"&&(r.error("网络错误，请稍后重试"),console.error("Error deleting item:",e))}},X=async()=>{if(f.value.length===0){r.warning("请选择要删除的数据");return}try{await D.confirm(`确定要删除选中的 ${f.value.length} 条轮播图吗？`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=f.value.map(i=>i.id),e=await U.delete("/api/swiper/batch",{data:a});e.data.code===200?(r.success("批量删除成功"),f.value=[],g()):r.error(e.data.message||"批量删除失败")}catch(a){a!=="cancel"&&(r.error("网络错误，请稍后重试"),console.error("Error batch deleting:",a))}},Y=async a=>{try{const e=await U.put("/api/swiper/status",null,{params:{id:a.id,status:a.status}});e.data.code===200?r.success("状态更新成功"):(r.error(e.data.message||"状态更新失败"),a.status=a.status===1?0:1)}catch(e){r.error("网络错误，请稍后重试"),a.status=a.status===1?0:1,console.error("Error updating status:",e)}},ee=a=>{const e=a.type.startsWith("image/"),i=a.size/1024/1024<2;return e?i?!0:(r.error("图片大小不能超过 2MB!"),!1):(r.error("只能上传图片文件!"),!1)},ae=a=>{console.log("Upload response:",a),a.code===200?(s.imageUrl=a.data,console.log("Image URL set to:",s.imageUrl),console.log("Full image URL will be:",V(s.imageUrl)),r.success("图片上传成功"),Me(()=>{console.log("Form imageUrl after nextTick:",s.imageUrl)})):r.error(a.message||"图片上传失败")},te=()=>{console.log("Image loaded successfully")},le=a=>{console.error("Image load error:",a),console.error("Failed to load image:",a.target.src),r.error("图片加载失败，请检查图片路径")};return _e(()=>{g()}),(a,e)=>{const i=Ee,_=fe,E=xe,p=ke,T=Se,oe=Ce,L=ve,m=Ie,se=Be,re=Te,ne=Ve,ie=Ue,de=Fe,ue=Re,F=$e,pe=De,me=ye,ce=he;return v(),x("div",je,[d("div",qe,[e[12]||(e[12]=d("h2",null,"轮播图管理",-1)),t(_,{type:"primary",onClick:J},{default:l(()=>[t(i,null,{default:l(()=>[t(y(R))]),_:1}),e[11]||(e[11]=u(" 新增轮播图 ",-1))]),_:1,__:[11]})]),d("div",Ae,[t(L,{inline:!0,model:c,class:"search-form"},{default:l(()=>[t(p,{label:"标题"},{default:l(()=>[t(E,{modelValue:c.title,"onUpdate:modelValue":e[0]||(e[0]=o=>c.title=o),placeholder:"请输入轮播图标题",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(p,{label:"状态"},{default:l(()=>[t(oe,{modelValue:c.status,"onUpdate:modelValue":e[1]||(e[1]=o=>c.status=o),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:l(()=>[t(T,{label:"启用",value:1}),t(T,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),t(p,null,{default:l(()=>[t(_,{type:"primary",onClick:A},{default:l(()=>[t(i,null,{default:l(()=>[t(y(ze))]),_:1}),e[13]||(e[13]=u(" 搜索 ",-1))]),_:1,__:[13]}),t(_,{onClick:W},{default:l(()=>[t(i,null,{default:l(()=>[t(y(Ne))]),_:1}),e[14]||(e[14]=u(" 重置 ",-1))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),d("div",We,[be((v(),I(ne,{data:B.value,style:{width:"100%"},onSelectionChange:P},{default:l(()=>[t(m,{type:"selection",width:"55"}),t(m,{type:"index",label:"序号",width:"80",index:o=>(n.pageNum-1)*n.pageSize+o+1},null,8,["index"]),t(m,{prop:"title",label:"标题","min-width":"150"}),t(m,{label:"图片",width:"150"},{default:l(o=>[d("div",Ge,[o.row.imageUrl?(v(),I(se,{key:0,src:V(o.row.imageUrl),"preview-src-list":[V(o.row.imageUrl)],style:{width:"120px",height:"80px"},fit:"cover",class:"table-image"},{error:l(()=>[d("div",He,[t(i,null,{default:l(()=>[t(y(Oe))]),_:1}),e[15]||(e[15]=d("span",null,"加载失败",-1))])]),_:2},1032,["src","preview-src-list"])):(v(),x("span",Pe,"暂无图片"))])]),_:1}),t(m,{prop:"linkUrl",label:"跳转链接","min-width":"200","show-overflow-tooltip":""}),t(m,{prop:"sortOrder",label:"排序",width:"80"}),t(m,{label:"状态",width:"100"},{default:l(o=>[t(re,{modelValue:o.row.status,"onUpdate:modelValue":k=>o.row.status=k,"active-value":1,"inactive-value":0,onChange:k=>Y(o.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(m,{prop:"createTime",label:"创建时间",width:"180"}),t(m,{label:"操作",width:"200",fixed:"right"},{default:l(o=>[t(_,{type:"primary",size:"small",onClick:k=>K(o.row)},{default:l(()=>e[16]||(e[16]=[u(" 编辑 ",-1)])),_:2,__:[16]},1032,["onClick"]),t(_,{type:"danger",size:"small",onClick:k=>Z(o.row)},{default:l(()=>e[17]||(e[17]=[u(" 删除 ",-1)])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ce,C.value]]),f.value.length>0?(v(),x("div",Je,[t(_,{type:"danger",onClick:X},{default:l(()=>[u(" 批量删除 ("+Le(f.value.length)+") ",1)]),_:1})])):we("",!0),d("div",Ke,[t(ie,{"current-page":n.pageNum,"onUpdate:currentPage":e[2]||(e[2]=o=>n.pageNum=o),"page-size":n.pageSize,"onUpdate:pageSize":e[3]||(e[3]=o=>n.pageSize=o),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:H},null,8,["current-page","page-size","total"])])]),t(me,{title:M.value,modelValue:b.value,"onUpdate:modelValue":e[10]||(e[10]=o=>b.value=o),width:"600px",onClose:O},{footer:l(()=>[d("span",Xe,[t(_,{onClick:e[9]||(e[9]=o=>b.value=!1)},{default:l(()=>e[21]||(e[21]=[u("取消",-1)])),_:1,__:[21]}),t(_,{type:"primary",onClick:Q,loading:S.value},{default:l(()=>e[22]||(e[22]=[u(" 确定 ",-1)])),_:1,__:[22]},8,["loading"])])]),default:l(()=>[t(L,{ref_key:"formRef",ref:h,model:s,rules:$,"label-width":"100px"},{default:l(()=>[t(p,{label:"标题",prop:"title"},{default:l(()=>[t(E,{modelValue:s.title,"onUpdate:modelValue":e[4]||(e[4]=o=>s.title=o),placeholder:"请输入轮播图标题"},null,8,["modelValue"])]),_:1}),t(p,{label:"图片",prop:"imageUrl"},{default:l(()=>[d("div",Qe,[t(de,{class:"image-uploader",action:j.value,"show-file-list":!1,"on-success":ae,"before-upload":ee,headers:q.value},{default:l(()=>[s.imageUrl?(v(),x("img",{src:V(s.imageUrl),class:"uploaded-image",onLoad:te,onError:le,key:s.imageUrl},null,40,Ze)):(v(),I(i,{key:1,class:"image-uploader-icon"},{default:l(()=>[t(y(R))]),_:1}))]),_:1},8,["action","headers"]),e[18]||(e[18]=d("div",{class:"upload-tip"},"支持 jpg、png 格式，大小不超过 2MB",-1))])]),_:1}),t(p,{label:"跳转链接"},{default:l(()=>[t(E,{modelValue:s.linkUrl,"onUpdate:modelValue":e[5]||(e[5]=o=>s.linkUrl=o),placeholder:"请输入跳转链接（可选）"},null,8,["modelValue"])]),_:1}),t(p,{label:"排序顺序",prop:"sortOrder"},{default:l(()=>[t(ue,{modelValue:s.sortOrder,"onUpdate:modelValue":e[6]||(e[6]=o=>s.sortOrder=o),min:0,max:999},null,8,["modelValue"])]),_:1}),t(p,{label:"状态"},{default:l(()=>[t(pe,{modelValue:s.status,"onUpdate:modelValue":e[7]||(e[7]=o=>s.status=o)},{default:l(()=>[t(F,{label:1},{default:l(()=>e[19]||(e[19]=[u("启用",-1)])),_:1,__:[19]}),t(F,{label:0},{default:l(()=>e[20]||(e[20]=[u("禁用",-1)])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"描述"},{default:l(()=>[t(E,{modelValue:s.description,"onUpdate:modelValue":e[8]||(e[8]=o=>s.description=o),type:"textarea",rows:3,placeholder:"请输入描述（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},ba=ge(Ye,[["__scopeId","data-v-bfbd9ec6"]]);export{ba as default};
