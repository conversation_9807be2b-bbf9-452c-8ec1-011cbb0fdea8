import{_ as _e}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css               */import{r as V,a as A,y as R,o as we,c as D,b as h,G as S,d as t,w as l,m as v,aO as be,s as he,aC as ve,I as ye,b8 as Ve,B as N,aJ as Ee,a5 as ke,b2 as xe,aR as F,a1 as Te,g as m,aD as Ce,i as x,q as c,aE as Ue,k as Se,N as L,aK as Ne,aL as $e,bn as Pe,aH as ze,P as Be,t as C,a3 as Ae,aP as Ie,E as Oe,j as Re,aF as De,b3 as Fe,ar as $}from"./index-D5n6Uv5f.js";const Le={class:"admin-users-container"},je={class:"page-header"},qe={class:"header-right"},Je={class:"search-section"},Me={class:"table-section"},Ge={class:"pagination-section"},He={key:0,class:"batch-actions"},Ke={class:"dialog-footer"},j="/api/admin",Qe={__name:"AdminUsers",setup(We){const P=V(!1),z=V(!1),E=V(!1),g=V(!1),B=V(),d=A({keyword:"",status:"",role:""}),u=A({page:1,size:20,total:0}),I=V([]),f=V([]),s=A({id:null,username:"",email:"",realName:"",password:"",confirm_password:"",role:"",status:1,phone:"",remark:""}),q=R(()=>{const a={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]};return g.value?a.password=[{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]:(a.password=[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],a.confirm_password=[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,r,n)=>{r!==s.password?n(new Error("两次输入密码不一致")):n()},trigger:"blur"}]),a}),J=R(()=>g.value?"编辑用户":"新增用户"),M=a=>({super_admin:"danger",admin:"warning",operator:"info"})[a]||"info",G=a=>({super_admin:"超级管理员",admin:"管理员",operator:"操作员"})[a]||"未知",y=async(a,e={})=>{const r=localStorage.getItem("adminToken"),n={mode:"cors",credentials:"include",headers:{"Content-Type":"application/json",Accept:"application/json",...r&&{Authorization:`Bearer ${r}`}}};console.log("API请求:",`${j}${a}`,e);try{const i=await fetch(`${j}${a}`,{...n,...e,headers:{...n.headers,...e.headers}});if(console.log("API响应状态:",i.status,i.statusText),!i.ok){const k=await i.text();throw console.error("API错误响应:",k),new Error(`HTTP ${i.status}: ${k}`)}const p=await i.json();return console.log("API响应数据:",p),p}catch(i){throw console.error("API请求失败:",i),i}},b=async()=>{P.value=!0;try{const a=new URLSearchParams({current:u.page.toString(),size:u.size.toString()});d.keyword&&a.append("keyword",d.keyword),d.status!==""&&a.append("status",d.status),d.role&&a.append("role",d.role);const e=await y(`/admin-users?${a}`);if(e.code===200)I.value=e.data.records,u.total=e.data.total;else throw new Error(e.message)}catch(a){console.error("获取用户列表失败:",a),m.error("获取用户列表失败: "+a.message)}finally{P.value=!1}},T=()=>{u.page=1,b()},H=()=>{Object.assign(d,{keyword:"",status:"",role:""}),T()},K=a=>{u.size=a,b()},Q=a=>{u.page=a,b()},W=a=>{f.value=a},X=async a=>{try{const e=await y(`/admin-users/${a.id}/status`,{method:"PUT",body:JSON.stringify({status:a.status})});if(e.code===200)m.success(`用户${a.status?"启用":"禁用"}成功`);else throw new Error(e.message)}catch(e){a.status=a.status?0:1,console.error("状态更新失败:",e),m.error("状态更新失败: "+e.message)}},Y=()=>{g.value=!1,ee(),E.value=!0},Z=a=>{g.value=!0,Object.assign(s,{...a}),E.value=!0},ee=()=>{var a;Object.assign(s,{id:null,username:"",email:"",realName:"",password:"",confirm_password:"",role:"",status:1,phone:"",remark:""}),(a=B.value)==null||a.clearValidate()},ae=a=>(u.page-1)*u.size+a+1,te=async()=>{try{await B.value.validate(),z.value=!0;const a={username:s.username,email:s.email,realName:s.realName,role:s.role,status:s.status,phone:s.phone||null,remark:s.remark||null};g.value||(a.password=s.password),g.value&&s.password&&s.password.trim()!==""&&(a.password=s.password);let e;if(g.value?e=await y(`/admin-users/${s.id}`,{method:"PUT",body:JSON.stringify(a)}):e=await y("/admin-users",{method:"POST",body:JSON.stringify(a)}),e.code===200)m.success(g.value?"用户更新成功":"用户创建成功"),E.value=!1,b();else throw new Error(e.message)}catch(a){console.error("操作失败:",a),m.error("操作失败: "+a.message)}finally{z.value=!1}},oe=async a=>{try{await $.confirm(`确定要删除用户 "${a.username}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await y(`/admin-users/${a.id}`,{method:"DELETE"});if(e.code===200)m.success("用户删除成功"),b();else throw new Error(e.message)}catch(e){e!=="cancel"&&(console.error("删除失败:",e),m.error("删除失败: "+e.message))}},le=async()=>{try{await $.confirm(`确定要启用选中的 ${f.value.length} 个用户吗？`,"批量启用确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const a=f.value.map(r=>r.id),e=await y("/admin-users/batch/status",{method:"PUT",body:JSON.stringify({ids:a,status:1})});if(e.code===200)m.success("批量启用成功"),b();else throw new Error(e.message)}catch(a){a!=="cancel"&&(console.error("批量启用失败:",a),m.error("批量启用失败: "+a.message))}},se=async()=>{try{await $.confirm(`确定要禁用选中的 ${f.value.length} 个用户吗？`,"批量禁用确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=f.value.map(r=>r.id),e=await y("/admin-users/batch/status",{method:"PUT",body:JSON.stringify({ids:a,status:0})});if(e.code===200)m.success("批量禁用成功"),b();else throw new Error(e.message)}catch(a){a!=="cancel"&&(console.error("批量禁用失败:",a),m.error("批量禁用失败: "+a.message))}},re=async()=>{try{await $.confirm(`确定要删除选中的 ${f.value.length} 个用户吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=f.value.map(r=>r.id),e=await y("/admin-users/batch",{method:"DELETE",body:JSON.stringify({ids:a})});if(e.code===200)m.success("批量删除成功"),b();else throw new Error(e.message)}catch(a){a!=="cancel"&&(console.error("批量删除失败:",a),m.error("批量删除失败: "+a.message))}};return we(()=>{b()}),(a,e)=>{const r=he,n=Se,i=Ue,p=$e,k=Ne,ne=ve,_=ze,ie=Be,de=Ae,ue=Ce,pe=Ee,w=Re,O=Fe,me=De,ce=Oe,ge=Te,fe=Ve;return x(),D("div",Le,[h("div",je,[e[18]||(e[18]=h("div",{class:"header-left"},[h("h1",{class:"page-title"},"后端用户管理"),h("p",{class:"page-description"},"管理系统后端管理员账户")],-1)),h("div",qe,[t(r,{type:"primary",onClick:Y,icon:v(be)},{default:l(()=>e[17]||(e[17]=[c(" 新增用户 ",-1)])),_:1,__:[17]},8,["icon"])])]),h("div",Je,[t(ne,{gutter:20},{default:l(()=>[t(i,{span:6},{default:l(()=>[t(n,{modelValue:d.keyword,"onUpdate:modelValue":e[0]||(e[0]=o=>d.keyword=o),placeholder:"搜索用户名、邮箱","prefix-icon":v(L),clearable:"",onInput:T},null,8,["modelValue","prefix-icon"])]),_:1}),t(i,{span:4},{default:l(()=>[t(k,{modelValue:d.status,"onUpdate:modelValue":e[1]||(e[1]=o=>d.status=o),placeholder:"状态筛选",clearable:"",onChange:T},{default:l(()=>[t(p,{label:"全部",value:""}),t(p,{label:"启用",value:"1"}),t(p,{label:"禁用",value:"0"})]),_:1},8,["modelValue"])]),_:1}),t(i,{span:4},{default:l(()=>[t(k,{modelValue:d.role,"onUpdate:modelValue":e[2]||(e[2]=o=>d.role=o),placeholder:"角色筛选",clearable:"",onChange:T},{default:l(()=>[t(p,{label:"全部",value:""}),t(p,{label:"超级管理员",value:"super_admin"}),t(p,{label:"管理员",value:"admin"}),t(p,{label:"操作员",value:"operator"})]),_:1},8,["modelValue"])]),_:1}),t(i,{span:4},{default:l(()=>[t(r,{type:"primary",onClick:T,icon:v(L)},{default:l(()=>e[19]||(e[19]=[c("搜索",-1)])),_:1,__:[19]},8,["icon"]),t(r,{onClick:H,icon:v(Pe)},{default:l(()=>e[20]||(e[20]=[c("重置",-1)])),_:1,__:[20]},8,["icon"])]),_:1})]),_:1})]),h("div",Me,[ye((x(),N(ue,{data:I.value,stripe:"",border:"",style:{width:"100%"},onSelectionChange:W},{default:l(()=>[t(_,{type:"selection",width:"55"}),t(_,{type:"index",label:"序号",width:"80",index:ae}),t(_,{prop:"username",label:"用户名",width:"120"}),t(_,{prop:"email",label:"邮箱",width:"200"}),t(_,{prop:"realName",label:"真实姓名",width:"120"}),t(_,{prop:"phone",label:"手机号",width:"130"}),t(_,{prop:"role",label:"角色",width:"120"},{default:l(({row:o})=>[t(ie,{type:M(o.role)},{default:l(()=>[c(C(G(o.role)),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"status",label:"状态",width:"100"},{default:l(({row:o})=>[t(de,{modelValue:o.status,"onUpdate:modelValue":U=>o.status=U,"active-value":1,"inactive-value":0,onChange:U=>X(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(_,{prop:"lastLoginTime",label:"最后登录",width:"160"}),t(_,{prop:"createdAt",label:"创建时间",width:"160"}),t(_,{label:"操作",width:"200",fixed:"right"},{default:l(({row:o})=>[t(r,{type:"primary",size:"small",onClick:U=>Z(o),icon:v(Ie)},{default:l(()=>e[21]||(e[21]=[c(" 编辑 ",-1)])),_:2,__:[21]},1032,["onClick","icon"]),t(r,{type:"danger",size:"small",onClick:U=>oe(o),icon:v(F)},{default:l(()=>e[22]||(e[22]=[c(" 删除 ",-1)])),_:2,__:[22]},1032,["onClick","icon"])]),_:1})]),_:1},8,["data"])),[[fe,P.value]]),h("div",Ge,[t(pe,{"current-page":u.page,"onUpdate:currentPage":e[3]||(e[3]=o=>u.page=o),"page-size":u.size,"onUpdate:pageSize":e[4]||(e[4]=o=>u.size=o),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),f.value.length>0?(x(),D("div",He,[t(r,{type:"success",onClick:le,icon:v(ke)},{default:l(()=>[c(" 批量启用 ("+C(f.value.length)+") ",1)]),_:1},8,["icon"]),t(r,{type:"warning",onClick:se,icon:v(xe)},{default:l(()=>[c(" 批量禁用 ("+C(f.value.length)+") ",1)]),_:1},8,["icon"]),t(r,{type:"danger",onClick:re,icon:v(F)},{default:l(()=>[c(" 批量删除 ("+C(f.value.length)+") ",1)]),_:1},8,["icon"])])):S("",!0),t(ge,{modelValue:E.value,"onUpdate:modelValue":e[16]||(e[16]=o=>E.value=o),title:J.value,width:"600px","close-on-click-modal":!1},{footer:l(()=>[h("span",Ke,[t(r,{onClick:e[15]||(e[15]=o=>E.value=!1)},{default:l(()=>e[25]||(e[25]=[c("取消",-1)])),_:1,__:[25]}),t(r,{type:"primary",onClick:te,loading:z.value},{default:l(()=>[c(C(g.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:l(()=>[t(ce,{ref_key:"userFormRef",ref:B,model:s,rules:q.value,"label-width":"100px"},{default:l(()=>[t(w,{label:"用户名",prop:"username"},{default:l(()=>[t(n,{modelValue:s.username,"onUpdate:modelValue":e[5]||(e[5]=o=>s.username=o),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),t(w,{label:"邮箱",prop:"email"},{default:l(()=>[t(n,{modelValue:s.email,"onUpdate:modelValue":e[6]||(e[6]=o=>s.email=o),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),t(w,{label:"真实姓名",prop:"realName"},{default:l(()=>[t(n,{modelValue:s.realName,"onUpdate:modelValue":e[7]||(e[7]=o=>s.realName=o),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),t(w,{label:"手机号",prop:"phone"},{default:l(()=>[t(n,{modelValue:s.phone,"onUpdate:modelValue":e[8]||(e[8]=o=>s.phone=o),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),g.value?S("",!0):(x(),N(w,{key:0,label:"密码",prop:"password"},{default:l(()=>[t(n,{modelValue:s.password,"onUpdate:modelValue":e[9]||(e[9]=o=>s.password=o),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})),g.value?S("",!0):(x(),N(w,{key:1,label:"确认密码",prop:"confirm_password"},{default:l(()=>[t(n,{modelValue:s.confirm_password,"onUpdate:modelValue":e[10]||(e[10]=o=>s.confirm_password=o),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])]),_:1})),g.value?(x(),N(w,{key:2,label:"重置密码",prop:"password"},{default:l(()=>[t(n,{modelValue:s.password,"onUpdate:modelValue":e[11]||(e[11]=o=>s.password=o),type:"password",placeholder:"留空则不修改密码","show-password":""},null,8,["modelValue"])]),_:1})):S("",!0),t(w,{label:"角色",prop:"role"},{default:l(()=>[t(k,{modelValue:s.role,"onUpdate:modelValue":e[12]||(e[12]=o=>s.role=o),placeholder:"请选择角色"},{default:l(()=>[t(p,{label:"超级管理员",value:"super_admin"}),t(p,{label:"管理员",value:"admin"}),t(p,{label:"操作员",value:"operator"})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"状态",prop:"status"},{default:l(()=>[t(me,{modelValue:s.status,"onUpdate:modelValue":e[13]||(e[13]=o=>s.status=o)},{default:l(()=>[t(O,{label:1},{default:l(()=>e[23]||(e[23]=[c("启用",-1)])),_:1,__:[23]}),t(O,{label:0},{default:l(()=>e[24]||(e[24]=[c("禁用",-1)])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"备注",prop:"remark"},{default:l(()=>[t(n,{modelValue:s.remark,"onUpdate:modelValue":e[14]||(e[14]=o=>s.remark=o),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},ma=_e(Qe,[["__scopeId","data-v-0e58e99c"]]);export{ma as default};
