import{_ as le}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                *//* empty css               *//* empty css                *//* empty css                 *//* empty css               *//* empty css                         */import{r as u,c as r,b as n,d as e,B as h,G as O,w as s,aC as oe,s as ne,aI as re,D as G,i as o,aE as ue,be as ce,Z as z,_ as D,P as de,q as c,t as d,Y as ie,l as _e,bi as ve,bp as pe,bq as me,f as M,g as Y}from"./index-BiIMV38A.js";const ge={class:"data-test"},ye={class:"card-header"},fe={key:0,class:"loading"},we={key:1,class:"data-list"},he={class:"item-desc"},ke={class:"test-result"},Ee={class:"card-header"},Ce={key:0,class:"loading"},Te={key:1,class:"data-list"},Ve={class:"item-price"},ze={class:"test-result"},De={class:"card-header"},Pe={key:0,class:"loading"},Se={key:1,class:"data-list"},be={class:"test-result"},Re={class:"card-header"},xe={key:0,class:"loading"},Be={key:1,class:"data-list"},Le={class:"item-code"},Ae={class:"test-result"},Me={class:"test-actions"},Ne={key:0},Ie={__name:"DataTest",setup($e){const N=u(!1),k=u([]),P=u(""),p=u("未测试"),I=u(!1),E=u([]),S=u(""),m=u("未测试"),$=u(!1),C=u([]),b=u(""),g=u("未测试"),F=u(!1),T=u([]),R=u(""),y=u("未测试"),q=u(!1),x=u([]),Z=async()=>{N.value=!0,P.value="",p.value="测试中...";try{const a=await M.getCategories();k.value=a.data.records||a.data||[],P.value="success",p.value=`成功加载 ${k.value.length} 条分类数据`,i("分类数据测试","success",p.value,k.value.length)}catch(a){console.error("分类数据测试失败:",a),P.value="error",p.value="加载失败: "+a.message,i("分类数据测试","error",p.value)}finally{N.value=!1}},j=async()=>{I.value=!0,S.value="",m.value="测试中...";try{const a=await M.getFlowers({current:1,size:10});E.value=a.data.records||a.data||[],S.value="success",m.value=`成功加载 ${E.value.length} 条商品数据`,i("商品数据测试","success",m.value,E.value.length)}catch(a){console.error("商品数据测试失败:",a),S.value="error",m.value="加载失败: "+a.message,i("商品数据测试","error",m.value)}finally{I.value=!1}},H=async()=>{$.value=!0,b.value="",g.value="测试中...";try{const a=await M.getReviews({current:1,size:10});C.value=a.data.records||a.data||[],b.value="success",g.value=`成功加载 ${C.value.length} 条评价数据`,i("评价数据测试","success",g.value,C.value.length)}catch(a){console.error("评价数据测试失败:",a),b.value="error",g.value="加载失败: "+a.message,i("评价数据测试","error",g.value)}finally{$.value=!1}},J=async()=>{F.value=!0,R.value="",y.value="测试中...";try{const a=await M.getProvinces();T.value=a.data||[],R.value="success",y.value=`成功加载 ${T.value.length} 条省份数据`,i("地址数据测试","success",y.value,T.value.length)}catch(a){console.error("地址数据测试失败:",a),R.value="error",y.value="加载失败: "+a.message,i("地址数据测试","error",y.value)}finally{F.value=!1}},Q=async()=>{q.value=!0,x.value=[],Y.info("开始测试所有数据加载功能..."),await Z(),await new Promise(a=>setTimeout(a,500)),await j(),await new Promise(a=>setTimeout(a,500)),await H(),await new Promise(a=>setTimeout(a,500)),await J(),q.value=!1,Y.success("所有数据加载测试完成！")},i=(a,t,f,_=null)=>{x.value.unshift({title:a,status:t,message:f,data:_,timestamp:new Date().toLocaleTimeString()})},W=()=>{Y.info("请通过左侧菜单访问各个管理页面查看实际效果")};return(a,t)=>{const f=G("Refresh"),_=_e,w=ne,B=ce,v=de,L=ie,V=re,A=ue,K=oe,X=ve,ee=G("PlayArrow"),se=G("View"),ae=me,te=pe;return o(),r("div",ge,[t[11]||(t[11]=n("div",{class:"page-header"},[n("h2",{class:"page-title"},"数据加载测试"),n("p",{class:"page-description"},"测试所有页面的数据加载功能")],-1)),e(K,{gutter:24},{default:s(()=>[e(A,{span:12},{default:s(()=>[e(V,{class:"test-card"},{header:s(()=>[n("div",ye,[t[1]||(t[1]=n("span",null,"分类数据测试",-1)),e(w,{type:"primary",size:"small",onClick:Z},{default:s(()=>[e(_,null,{default:s(()=>[e(f)]),_:1}),t[0]||(t[0]=c(" 测试 ",-1))]),_:1,__:[0]})])]),default:s(()=>[N.value?(o(),r("div",fe,[e(B,{rows:3,animated:""})])):k.value.length>0?(o(),r("div",we,[(o(!0),r(z,null,D(k.value,l=>(o(),r("div",{key:l.id,class:"data-item"},[e(v,{type:l.status===1?"success":"danger"},{default:s(()=>[c(d(l.name),1)]),_:2},1032,["type"]),n("span",he,d(l.description),1)]))),128))])):(o(),h(L,{key:2,description:"暂无数据"})),n("div",ke,[e(v,{type:P.value==="success"?"success":"danger"},{default:s(()=>[c(d(p.value),1)]),_:1},8,["type"])])]),_:1})]),_:1}),e(A,{span:12},{default:s(()=>[e(V,{class:"test-card"},{header:s(()=>[n("div",Ee,[t[3]||(t[3]=n("span",null,"商品数据测试",-1)),e(w,{type:"primary",size:"small",onClick:j},{default:s(()=>[e(_,null,{default:s(()=>[e(f)]),_:1}),t[2]||(t[2]=c(" 测试 ",-1))]),_:1,__:[2]})])]),default:s(()=>[I.value?(o(),r("div",Ce,[e(B,{rows:3,animated:""})])):E.value.length>0?(o(),r("div",Te,[(o(!0),r(z,null,D(E.value,l=>(o(),r("div",{key:l.id,class:"data-item"},[e(v,{type:l.status===1?"success":"danger"},{default:s(()=>[c(d(l.name),1)]),_:2},1032,["type"]),n("span",Ve,"¥"+d(l.price),1)]))),128))])):(o(),h(L,{key:2,description:"暂无数据"})),n("div",ze,[e(v,{type:S.value==="success"?"success":"danger"},{default:s(()=>[c(d(m.value),1)]),_:1},8,["type"])])]),_:1})]),_:1})]),_:1}),e(K,{gutter:24,style:{"margin-top":"24px"}},{default:s(()=>[e(A,{span:12},{default:s(()=>[e(V,{class:"test-card"},{header:s(()=>[n("div",De,[t[5]||(t[5]=n("span",null,"评价数据测试",-1)),e(w,{type:"primary",size:"small",onClick:H},{default:s(()=>[e(_,null,{default:s(()=>[e(f)]),_:1}),t[4]||(t[4]=c(" 测试 ",-1))]),_:1,__:[4]})])]),default:s(()=>[$.value?(o(),r("div",Pe,[e(B,{rows:3,animated:""})])):C.value.length>0?(o(),r("div",Se,[(o(!0),r(z,null,D(C.value,l=>(o(),r("div",{key:l.id,class:"data-item"},[e(v,{type:l.status===1?"success":"danger"},{default:s(()=>[c(d(l.userName),1)]),_:2},1032,["type"]),e(X,{modelValue:l.rating,"onUpdate:modelValue":U=>l.rating=U,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(o(),h(L,{key:2,description:"暂无数据"})),n("div",be,[e(v,{type:b.value==="success"?"success":"danger"},{default:s(()=>[c(d(g.value),1)]),_:1},8,["type"])])]),_:1})]),_:1}),e(A,{span:12},{default:s(()=>[e(V,{class:"test-card"},{header:s(()=>[n("div",Re,[t[7]||(t[7]=n("span",null,"地址数据测试",-1)),e(w,{type:"primary",size:"small",onClick:J},{default:s(()=>[e(_,null,{default:s(()=>[e(f)]),_:1}),t[6]||(t[6]=c(" 测试 ",-1))]),_:1,__:[6]})])]),default:s(()=>[F.value?(o(),r("div",xe,[e(B,{rows:3,animated:""})])):T.value.length>0?(o(),r("div",Be,[(o(!0),r(z,null,D(T.value.slice(0,5),l=>(o(),r("div",{key:l.code,class:"data-item"},[e(v,{type:"info"},{default:s(()=>[c(d(l.name),1)]),_:2},1024),n("span",Le,d(l.code),1)]))),128))])):(o(),h(L,{key:2,description:"暂无数据"})),n("div",Ae,[e(v,{type:R.value==="success"?"success":"danger"},{default:s(()=>[c(d(y.value),1)]),_:1},8,["type"])])]),_:1})]),_:1})]),_:1}),n("div",Me,[e(w,{type:"primary",size:"large",onClick:Q,loading:q.value},{default:s(()=>[e(_,null,{default:s(()=>[e(ee)]),_:1}),t[8]||(t[8]=c(" 测试所有数据加载 ",-1))]),_:1,__:[8]},8,["loading"]),e(w,{type:"success",size:"large",onClick:W},{default:s(()=>[e(_,null,{default:s(()=>[e(se)]),_:1}),t[9]||(t[9]=c(" 查看实际页面 ",-1))]),_:1,__:[9]})]),x.value.length>0?(o(),h(V,{key:0,class:"summary-card"},{header:s(()=>t[10]||(t[10]=[n("span",null,"测试结果汇总",-1)])),default:s(()=>[e(te,null,{default:s(()=>[(o(!0),r(z,null,D(x.value,(l,U)=>(o(),h(ae,{key:U,type:l.status==="success"?"success":"danger",timestamp:l.timestamp},{default:s(()=>[n("h4",null,d(l.title),1),n("p",null,d(l.message),1),l.data?(o(),r("p",Ne,"数据量: "+d(l.data)+"条",1)):O("",!0)]),_:2},1032,["type","timestamp"]))),128))]),_:1})]),_:1})):O("",!0)])}}},Je=le(Ie,[["__scopeId","data-v-50311c6e"]]);export{Je as default};
