import{_ as F}from"./_plugin-vue_export-helper-BXfX6KIN.js";/* empty css                        *//* empty css                    *//* empty css                     *//* empty css               *//* empty css                        *//* empty css                *//* empty css                             *//* empty css                  *//* empty css                         */import{r as h,o as G,c as v,b as n,d as e,w as t,s as H,be as U,aI as W,bf as J,f as $,z as K,g as f,h as Q,D as X,i as _,q as s,l as Y,m as u,bg as I,aM as ee,aN as te,t as o,P as ae,B as y,a8 as le,G as b,Z as C,aD as se,aH as oe,aZ as ne,bb as ue,al as re,aW as ie,b7 as de,aP as pe,bh as _e,ar as A}from"./index-DylWFde9.js";import{g as P,a as N,f as m,b as w}from"./index-Bpa9ymHh.js";const ce={class:"page-container"},ve={class:"page-header"},me={key:0,class:"loading-container"},fe={key:1,class:"order-detail"},ye={class:"card-header"},be={class:"user-detail"},ke={class:"user-text"},ge={class:"user-name"},Te={class:"user-phone"},Ae={class:"amount-detail"},we={key:0,class:"discount"},Ee={class:"final-amount"},xe={class:"payment-info"},Be={class:"card-header"},$e={class:"store-address"},Ce={class:"order-summary"},Ne={class:"summary-item"},Oe={class:"summary-item total"},De={class:"total-amount"},he={class:"order-actions"},Ie={class:"bottom-actions"},Pe={key:2,class:"error-container"},Se={__name:"OrderDetail",setup(Me){const S=K();Q();const E=h(!1),a=h(null),x=async()=>{E.value=!0;try{const d=await $.getOrderDetail(S.params.id);a.value=d.data}catch(d){console.error("加载订单详情失败:",d),f.error("加载订单详情失败")}finally{E.value=!1}},B=async d=>{const l=N(d);try{await A.confirm(`确定要将订单状态更新为 "${l}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await $.updateOrderStatus(a.value.id,d),f.success("状态更新成功"),x()}catch(p){p!=="cancel"&&(console.error("更新订单状态失败:",p),f.error("更新订单状态失败"))}},M=async()=>{try{await A.confirm("确定要确认客户已完成付款吗？确认后用户将可以确认收货。","确认付款",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await $.confirmPayment(a.value.id),f.success("付款确认成功，等待用户确认收货"),x()}catch(d){d!=="cancel"&&(console.error("确认付款失败:",d),f.error("确认付款失败"))}},V=()=>{A.confirm("确定要修改此订单吗？修改后需要重新确认订单信息。","修改订单确认",{confirmButtonText:"确定修改",cancelButtonText:"取消",type:"warning"}).then(()=>{q()}).catch(()=>{})},q=()=>{const d=["修改收货信息","修改配送方式","修改订单备注","修改订单金额"];A.confirm(`请选择要修改的内容：

${d.map((l,p)=>`${p+1}. ${l}`).join(`
`)}

注意：修改订单可能会影响配送时间和费用`,"选择修改内容",{confirmButtonText:"继续修改",cancelButtonText:"取消",type:"info"}).then(()=>{f.info("订单修改功能开发中，请联系管理员手动修改")}).catch(()=>{})};return G(()=>{x()}),(d,l)=>{const p=Y,c=H,z=U,g=ae,r=te,O=le,D=ee,T=W,R=X("Location"),L=ne,k=oe,Z=se,j=J;return _(),v("div",ce,[n("div",ve,[l[8]||(l[8]=n("h2",{class:"page-title"},"订单详情",-1)),e(c,{onClick:l[0]||(l[0]=i=>d.$router.go(-1))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(I))]),_:1}),l[7]||(l[7]=s(" 返回 ",-1))]),_:1,__:[7]})]),E.value?(_(),v("div",me,[e(z,{rows:10,animated:""})])):a.value?(_(),v("div",fe,[e(T,{class:"mb-16"},{header:t(()=>[n("div",ye,[l[9]||(l[9]=n("span",null,"订单信息",-1)),e(g,{type:u(P)(a.value.status)},{default:t(()=>[s(o(u(N)(a.value.status)),1)]),_:1},8,["type"])])]),default:t(()=>[e(D,{column:2,border:""},{default:t(()=>[e(r,{label:"订单号"},{default:t(()=>[s(o(a.value.orderNo||a.value.id),1)]),_:1}),e(r,{label:"订单状态"},{default:t(()=>[e(g,{type:u(P)(a.value.status)},{default:t(()=>[s(o(u(N)(a.value.status)),1)]),_:1},8,["type"])]),_:1}),e(r,{label:"用户信息"},{default:t(()=>[n("div",be,[a.value.userAvatar?(_(),y(O,{key:0,src:a.value.userAvatar,size:36,fit:"cover",class:"detail-user-avatar"},null,8,["src"])):(_(),y(O,{key:1,size:36,class:"detail-user-avatar"},{default:t(()=>{var i;return[s(o(((i=a.value.userName)==null?void 0:i.charAt(0))||"U"),1)]}),_:1})),n("div",ke,[n("div",ge,o(a.value.userName||"未知用户"),1),n("div",Te,o(a.value.userPhone||"未绑定手机"),1)])])]),_:1}),e(r,{label:"配送方式"},{default:t(()=>[e(g,{type:a.value.deliveryType===1?"primary":"success"},{default:t(()=>[s(o(a.value.deliveryType===1?"外卖配送":"到店自取"),1)]),_:1},8,["type"])]),_:1}),e(r,{label:"订单金额"},{default:t(()=>[n("div",Ae,[n("div",null,"总金额：¥"+o(u(m)(a.value.totalAmount)),1),a.value.discountAmount&&a.value.discountAmount>0?(_(),v("div",we," 优惠：-¥"+o(u(m)(a.value.discountAmount)),1)):b("",!0),n("div",Ee,"实付：¥"+o(u(m)(a.value.finalAmount||a.value.totalAmount)),1)])]),_:1}),e(r,{label:"支付信息"},{default:t(()=>[n("div",xe,[n("div",null,"支付方式："+o(a.value.paymentMethod||"未支付"),1),n("div",null,"支付状态："+o(a.value.paymentStatus===1?"已支付":"未支付"),1)])]),_:1}),e(r,{label:"下单时间"},{default:t(()=>[s(o(u(w)(a.value.createdAt)),1)]),_:1}),e(r,{label:"支付时间"},{default:t(()=>[s(o(a.value.paidAt?u(w)(a.value.paidAt):"未支付"),1)]),_:1}),e(r,{label:"发货时间"},{default:t(()=>[s(o(a.value.shippedAt?u(w)(a.value.shippedAt):"未发货"),1)]),_:1}),e(r,{label:"完成时间"},{default:t(()=>[s(o(a.value.deliveredAt?u(w)(a.value.deliveredAt):"未完成"),1)]),_:1}),e(r,{label:"备注",span:2},{default:t(()=>[s(o(a.value.remark||"无"),1)]),_:1})]),_:1})]),_:1}),e(T,{class:"mb-16"},{header:t(()=>[n("div",Be,[l[10]||(l[10]=n("span",null,"配送信息",-1)),e(g,{type:a.value.deliveryType===1?"primary":"success"},{default:t(()=>[s(o(a.value.deliveryType===1?"外卖配送":"到店自取"),1)]),_:1},8,["type"])])]),default:t(()=>[e(D,{column:2,border:""},{default:t(()=>[a.value.deliveryType===1?(_(),v(C,{key:0},[e(r,{label:"收货人"},{default:t(()=>[s(o(a.value.recipientName||"未设置"),1)]),_:1}),e(r,{label:"联系电话"},{default:t(()=>[s(o(a.value.recipientPhone||"未设置"),1)]),_:1}),e(r,{label:"收货地址",span:2},{default:t(()=>[s(o(a.value.recipientAddress||"未设置"),1)]),_:1}),e(r,{label:"期望送达时间",span:2},{default:t(()=>[s(o(a.value.deliveryTime||"未设置"),1)]),_:1}),e(r,{label:"配送备注",span:2},{default:t(()=>[s(o(a.value.deliveryNotes||"无"),1)]),_:1})],64)):(_(),v(C,{key:1},[e(r,{label:"取货人"},{default:t(()=>[s(o(a.value.pickupName||"未设置"),1)]),_:1}),e(r,{label:"取货电话"},{default:t(()=>[s(o(a.value.pickupPhone||"未设置"),1)]),_:1}),e(r,{label:"期望取货时间",span:2},{default:t(()=>[s(o(a.value.pickupTime||"未设置"),1)]),_:1}),e(r,{label:"店铺地址",span:2},{default:t(()=>[n("div",$e,[e(p,null,{default:t(()=>[e(R)]),_:1}),l[11]||(l[11]=n("span",null,"花店地址：请联系商家确认具体取货地址",-1))])]),_:1})],64))]),_:1})]),_:1}),e(T,{class:"mb-16"},{header:t(()=>l[12]||(l[12]=[n("span",null,"商品信息",-1)])),default:t(()=>[e(Z,{data:a.value.items,stripe:""},{default:t(()=>[e(k,{label:"商品图片",width:"100"},{default:t(({row:i})=>[e(L,{src:i.flowerImage,"preview-src-list":[i.flowerImage],style:{width:"60px",height:"60px"},fit:"cover"},null,8,["src","preview-src-list"])]),_:1}),e(k,{prop:"flowerName",label:"商品名称"}),e(k,{prop:"price",label:"单价"},{default:t(({row:i})=>[s(" ¥"+o(u(m)(i.price)),1)]),_:1}),e(k,{prop:"quantity",label:"数量"}),e(k,{label:"小计"},{default:t(({row:i})=>[s(" ¥"+o(u(m)(i.price*i.quantity)),1)]),_:1})]),_:1},8,["data"]),n("div",Ce,[n("div",Ne,[l[13]||(l[13]=n("span",null,"商品总额：",-1)),n("span",null,"¥"+o(u(m)(a.value.totalAmount)),1)]),n("div",Oe,[l[14]||(l[14]=n("span",null,"订单总额：",-1)),n("span",De,"¥"+o(u(m)(a.value.totalAmount)),1)])])]),_:1}),e(T,null,{header:t(()=>l[15]||(l[15]=[n("span",null,"订单操作",-1)])),default:t(()=>[n("div",he,[a.value.status!==4&&a.value.status!==5?(_(),v(C,{key:0},[a.value.status===2?(_(),y(c,{key:0,type:"success",onClick:l[1]||(l[1]=i=>B(3))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(ue))]),_:1}),l[16]||(l[16]=s(" 开始配送 ",-1))]),_:1,__:[16]})):b("",!0),a.value.status===3?(_(),y(c,{key:1,type:"primary",onClick:l[2]||(l[2]=i=>B(1))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(re))]),_:1}),l[17]||(l[17]=s(" 确认送达 ",-1))]),_:1,__:[17]})):b("",!0),a.value.status===1&&a.value.paymentStatus===0?(_(),y(c,{key:2,type:"success",onClick:M},{default:t(()=>[e(p,null,{default:t(()=>[e(u(ie))]),_:1}),l[18]||(l[18]=s(" 确认付款 ",-1))]),_:1,__:[18]})):b("",!0),a.value.status<4?(_(),y(c,{key:3,type:"danger",onClick:l[3]||(l[3]=i=>B(5))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(de))]),_:1}),l[19]||(l[19]=s(" 取消订单 ",-1))]),_:1,__:[19]})):b("",!0)],64)):b("",!0),e(c,{type:"warning",onClick:V},{default:t(()=>[e(p,null,{default:t(()=>[e(u(pe))]),_:1}),l[20]||(l[20]=s(" 修改订单 ",-1))]),_:1,__:[20]})])]),_:1}),n("div",Ie,[e(c,{type:"primary",onClick:l[4]||(l[4]=i=>d.$router.go(-1))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(I))]),_:1}),l[21]||(l[21]=s(" 返回上一页 ",-1))]),_:1,__:[21]}),e(c,{onClick:l[5]||(l[5]=i=>d.$router.push("/orders"))},{default:t(()=>[e(p,null,{default:t(()=>[e(u(_e))]),_:1}),l[22]||(l[22]=s(" 返回订单列表 ",-1))]),_:1,__:[22]})])])):(_(),v("div",Pe,[e(j,{icon:"error",title:"订单不存在","sub-title":"请检查订单号是否正确"},{extra:t(()=>[e(c,{type:"primary",onClick:l[6]||(l[6]=i=>d.$router.push("/orders"))},{default:t(()=>l[23]||(l[23]=[s(" 返回订单列表 ",-1)])),_:1,__:[23]})]),_:1})]))])}}},Je=F(Se,[["__scopeId","data-v-80c0f817"]]);export{Je as default};
